<?php $__env->startPush('css'); ?>
    <style>
        .page-hero {
            background: linear-gradient(135deg, var(--deep-blue) 0%, var(--ocean-blue) 100%);
            padding: 5rem 0 3rem;
            position: relative;
            overflow: hidden;
        }

        .page-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .page-hero-content {
            position: relative;
            z-index: 2;
        }

        .page-content {
            padding: 4rem 0;
            background: var(--white);
        }

        .content-wrapper {
            background: var(--white);
            border-radius: 1rem;
            box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
            padding: 3rem;
            margin-top: -2rem;
            position: relative;
            z-index: 3;
        }

        .breadcrumb-custom {
            background: transparent;
            padding: 0;
            margin-bottom: 1rem;
        }

        .breadcrumb-custom .breadcrumb-item {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: var(--white);
            font-weight: 600;
        }

        .breadcrumb-custom .breadcrumb-item+.breadcrumb-item::before {
            content: "›";
            color: rgba(255, 255, 255, 0.6);
            font-weight: bold;
        }

        .breadcrumb-custom a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb-custom a:hover {
            color: var(--white);
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            color: var(--white);
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0;
            font-weight: 400;
        }

        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--light-black);
        }

        .content-text h1,
        .content-text h2,
        .content-text h3,
        .content-text h4,
        .content-text h5,
        .content-text h6 {
            color: var(--dark-blue);
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .content-text h1 {
            font-size: 2.5rem;
        }

        .content-text h2 {
            font-size: 2rem;
        }

        .content-text h3 {
            font-size: 1.75rem;
        }

        .content-text h4 {
            font-size: 1.5rem;
        }

        .content-text h5 {
            font-size: 1.25rem;
        }

        .content-text h6 {
            font-size: 1.1rem;
        }

        .content-text p {
            margin-bottom: 1.5rem;
        }

        .content-text ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }

        .content-text ul {
            padding-left: 0;
        }

        .content-text li {
            margin-bottom: 0.5rem;
        }

        .content-text blockquote {
            border-left: 4px solid var(--deep-blue);
            padding-left: 1.5rem;
            margin: 2rem 0;
            font-style: italic;
            color: var(--steel-blue);
            background: var(--ice-blue);
            padding: 1.5rem;
            border-radius: 0.5rem;
        }

        .content-text table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            background: var(--white);
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
        }

        .content-text th,
        .content-text td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .content-text th {
            background: var(--deep-blue);
            color: var(--white);
            font-weight: 600;
        }

        .content-text tr:hover {
            background: var(--ice-blue);
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--deep-blue);
            text-decoration: none;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border: 2px solid var(--deep-blue);
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-button:hover {
            background: var(--deep-blue);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(2, 12, 135, 0.3);
        }

        .decorative-element {
            position: absolute;
            right: -5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 15rem;
            height: 15rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            opacity: 0.5;
        }

        .decorative-element::before {
            content: '';
            position: absolute;
            top: 2rem;
            left: 2rem;
            right: 2rem;
            bottom: 2rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
        }

        .page-content p img {
            height: 70vh;
            width: 100%;
        }

        ul,
        ul li {
            display: flex;
            gap: 6px;
            margin-left: 0;
            font-size: 16px;
        }

        li img {
            border-radius: 50%;
        }


        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }

            .content-wrapper {
                padding: 2rem 1.5rem;
                margin-top: -1rem;
            }

            .decorative-element {
                display: none;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Hero Section -->
    <section class="page-hero">
        <div class="container">
            <div class="page-hero-content">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="page-title sora">
                            <?php if($page->heading): ?>
                                <?php
                                    $words = explode(' ', trim($page->heading));
                                    if (count($words) > 1) {
                                        $lastWord = array_pop($words);
                                        $remainingWords = implode(' ', $words);
                                        echo $remainingWords .
                                            ' <span class="gradient_heading">' .
                                            $lastWord .
                                            '</span>';
                                    } else {
                                        echo '<span class="gradient_heading">' . $page->heading . '</span>';
                                    }
                                ?>
                            <?php endif; ?>
                        </h1>
                    </div>
                    <div class="col-lg-4 position-relative">
                        <div class="decorative-element"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="page-content">
        <div class="container">
            <div class="content-wrapper">
                <div class="content-text">
                    <?php if($page->description): ?>
                        <?php echo $page->description; ?>

                    <?php else: ?>
                        <p class="text-muted">No content available for this page.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <?php if($page->code_snippets): ?>
        <script>
            <?php echo strip_tags(html_entity_decode($page->code_snippets)); ?>

        </script>
    <?php endif; ?>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/website/show.blade.php ENDPATH**/ ?>