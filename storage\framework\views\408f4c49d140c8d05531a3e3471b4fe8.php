<div>
    <form
        action="<?php echo e(isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'group']) : route('services.store', ['type' => 'group'])); ?>"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="groupServiceForm">
        <?php echo csrf_field(); ?>
        <?php if(isset($service)): ?>
            <?php echo method_field('PUT'); ?>
        <?php endif; ?>
        <div class="row row-gap-5">
            
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name<span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="<?php echo e(old('name', $service->name ?? '')); ?>">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            


            
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category<span class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <option value="<?php echo e($category->ids); ?>"
                            <?php echo e(old('category_id', $service->category->ids ?? '') == $category->ids ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <option value="">No categories found</option>
                    <?php endif; ?>
                </select>
                <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category<span class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    <?php if(isset($service) && $service->category): ?>
                        <?php $__empty_1 = true; $__currentLoopData = $service->category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <option value="<?php echo e($subcategory->ids); ?>"
                                <?php echo e(old('subcategory_id', $service->subcategory->ids ?? '') == $subcategory->ids ? 'selected' : ''); ?>>
                                <?php echo e($subcategory->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </select>
                <?php $__errorArgs = ['subcategory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            <?php if(auth()->check() && auth()->user()->hasRole('business')): ?>
                <div class="col-md-12">
                    <label for="staff-member-secondary" class="form-label form-input-labels">Assign Staff
                        Members<span class="text-danger">*</span></label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member-secondary" name="staff_ids[]">
                        <?php $__empty_1 = true; $__currentLoopData = $staffs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <option value="<?php echo e($staff->id); ?>"
                                <?php echo e(isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : ''); ?>>
                                <?php echo e($staff->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <option disabled>No staff members available</option>
                        <?php endif; ?>
                    </select>
                    <?php $__errorArgs = ['staff_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-danger">
                            <?php echo e($message); ?>

                        </p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            <?php endif; ?>

            
            <div class="col-md-12">
                <label class="form-label form-input-labels">Availability<span class="text-danger">*</span></label>
                <?php if (isset($component)) { $__componentOriginal81b877f93d7de2161525fd317bf6d57d = $component; } ?>
<?php $component = App\View\Components\ServiceAvailabilityComponent::resolve(['availabilities' => $service->availabilities ?? []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('service-availability-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ServiceAvailabilityComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal81b877f93d7de2161525fd317bf6d57d)): ?>
<?php $component = $__componentOriginal81b877f93d7de2161525fd317bf6d57d; ?>
<?php unset($__componentOriginal81b877f93d7de2161525fd317bf6d57d); ?>
<?php endif; ?>
                
                <input type="hidden" name="availability_check" id="availability_check" value="">
                
                <div id="availability-error-container"></div>
            </div>
            

            
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels">Service Duration<span class="text-danger">*</span></label>
                <select name="duration" id="duration" class="form-control form-select-field">
                    <option value="">Select Service Duration</option>
                    <option value="15" <?php echo e(old('duration', $service->duration ?? '') == '15' ? 'selected' : ''); ?>>15
                        min
                    </option>
                    <option value="30" <?php echo e(old('duration', $service->duration ?? '') == '30' ? 'selected' : ''); ?>>30
                        min
                    </option>
                    <option value="45" <?php echo e(old('duration', $service->duration ?? '') == '45' ? 'selected' : ''); ?>>40
                        min
                    </option>
                    <option value="60" <?php echo e(old('duration', $service->duration ?? '') == '60' ? 'selected' : ''); ?>>60
                        min
                    </option>
                </select>
                <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            



            
            
            <div class="col-md-6">
                <label for="discount_recurring" class="form-label form-input-labels">Discount For Recurring
                    <span class="normal opacity-6 light-black">(For more than 1 slot)<span class="text-danger">*</span></span></label>
                <div class="input-group">
                    <span class="input-group-text" id="basic-addon1">%</span>
                    <input type="text" class="form-control form-inputs-field" placeholder="Enter discount %"
                        id="discount_recurring" name="discount_recurring"
                        value="<?php echo e(old('discount_recurring', $service->discount_recurring ?? '')); ?>" />
                </div>
                <?php $__errorArgs = ['discount_recurring'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            
            <div class="col-md-6">
                <label for="total-slots" class="form-label form-input-labels">Total Slots<span class="text-danger">*</span></label>
                <input type="number" min="1" class="form-control form-inputs-field"
                    value="<?php echo e(old('total_slots', $service->total_slots ?? '')); ?>" placeholder="Enter total slots"
                    id="total-slots" name="total_slots">
                <?php $__errorArgs = ['total_slots'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-3">
                <label for="price-per-slot" class="form-label form-input-labels">Price Per Slot
                    <span class="normal opacity-6 light-black">(Inclusive VAT)<span class="text-danger">*</span></span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price"
                    value="<?php echo e(old('price_per_slot', $service->price ?? '')); ?>" id="price-per-slot"
                    name="price_per_slot">
                <?php $__errorArgs = ['price_per_slot'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-3">
                <label for="additional-costs" class="form-label form-input-labels">Additional
                    Costs<span class="text-danger">*</span></label>
                <input type="number" class="form-control form-inputs-field"
                    value="<?php echo e(old('additional_cost', $service->additional_cost ?? '')); ?>"
                    placeholder="Enter additional  costs" id="additional-costs" name="additional_cost">
                <?php $__errorArgs = ['additional-costs'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service<span class="text-danger">*</span></label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="<?php echo e(old('required_items', $service->required_items ?? '')); ?>">
                <?php $__errorArgs = ['required_items'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description<span class="text-danger">*</span></label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here"><?php echo e(old('description', $service->description ?? '')); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            

            
            <div class="col-md-4 d-flex gap-4">
                <label class="d-flex gap-2 align-items-center">
                    <input class="form-check-input" type="radio" name="service_location" value="onsite"
                        id="onsite-radio" <?php if(old('service_location', $service->is_onsite ?? false) == 'onsite' ||
                                old('service_location', $service->is_onsite ?? false) == true): echo 'checked'; endif; ?>>
                    <span>On-site</span>
                </label>
                <label class="d-flex gap-2 align-items-center">
                    <input class="form-check-input" type="radio" name="service_location" value="outside-location"
                        id="outside-location-radio" <?php if(old('service_location', $service->outside_location ?? false) == 'outside-location' ||
                                (old('service_location') == null && ($service->outside_location ?? false) == true)): echo 'checked'; endif; ?>>
                    <span>Outside Location</span>
                </label>
            </div>
            <?php $__errorArgs = ['service_location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-danger">
                    <?php echo e($message); ?>

                </p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            

            
            <div class="row row-gap-5">
                <!-- Physical Location -->
                <div class="col-md-12 form-hide-box" id="physical-location-seconday-field">
                    <label for="physical-location-secondary" class="form-label form-input-labels">Physical
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field" id="pac-input"
                        name="physical_location" placeholder="2715 Ash Dr. San Jose, South Dakota 83475"
                        value="<?php echo e(old('physical_location', $service->physical_location ?? '')); ?>">
                    <input type="hidden" name="lat" value="" id="latitude">
                    <input type="hidden" name="lng" value="" id="longitude">
                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px"></div>
                    </div>
                </div>

                <!-- Outside Location -->
                <div class="col-md-12 form-hide-box" id="outside-location-field">
                    <label for="outside-location-secondary" class="form-label form-input-labels">Outside
                        Location</label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field"
                        id="outside-location-secondary" name="outside_location"
                        placeholder="Enter your outside location"
                        value="<?php echo e(old('outside_location', $service->outside_location ?? '')); ?>">
                </div>
                <?php $__errorArgs = ['outside_location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <?php $__errorArgs = ['physical_location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            


            <!-- Thumbnail Image -->
            <div class="col-md-4 ">
                <label for="thumbnail-secondary" class="form-label form-input-labels">Thumbnail
                    Image<span class="text-danger">*</span></label>
                <div class="position-relative  form-add-category">
                    <div class="image-input <?php echo e($service->image ?? null ? 'image-input-changed' : 'image-input-empty'); ?>"
                        data-kt-image-input="true">
                        <div class="image-input-wrapper"
                            style="background-image: url('<?php echo e(asset('website') . '/' . ($service->image ?? '')); ?>');">
                        </div>
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>50x50 px</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                        </label>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                            title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>
                    </div>
                </div>
                <?php $__errorArgs = ['thumbnail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-danger">
                        <?php echo e($message); ?>

                    </p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <div class="">
                <button type="submit" class="add-btn">
                    <?php echo e($btn_text ?? 'Add'); ?>

                </button>
            </div>
        </div>
    </form>
</div>
<?php $__env->startPush('js'); ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                if (typeof $.fn.validate !== 'undefined') {
                    $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                        if (element.files.length === 0) {
                            return true; // no file selected, let 'required' rule handle this
                        }
                        const fileSizeKB = element.files[0].size / 1024; // size in KB
                        return fileSizeKB <= maxSizeKB;
                    }, 'File size must be less than {0} KB.');
                    // Add custom validation method for role-based requirements
                    $.validator.addMethod('requiredForRole', function(value, element, params) {
                        const userRole = params.role;
                        const currentUserRole =
                            '<?php echo e(auth()->user()->getRoleNames()->first()); ?>';

                        // If current user has the specified role, field is required
                        if (currentUserRole === userRole) {
                            return value && value.length > 0;
                        }
                        // If user doesn't have the role, field is not required
                        return true;
                    }, 'This field is required for your role.');

                    // Add custom validation method for conditional requirements based on checkboxes
                    $.validator.addMethod('requiredIfChecked', function(value, element, params) {
                        const checkboxSelector = params.checkbox;
                        const isChecked = $(checkboxSelector).is(':checked');

                        // If checkbox is checked, field is required
                        if (isChecked) {
                            return value && value.trim().length > 0;
                        }
                        // If checkbox is not checked, field is not required
                        return true;
                    }, 'This field is required when the related option is selected.');

                    // Add validation for multiple roles
                    $.validator.addMethod('requiredForRoles', function(value, element, params) {
                        const requiredRoles = params.roles; // Array of roles
                        const currentUserRole =
                            '<?php echo e(auth()->user()->getRoleNames()->first()); ?>';

                        // If current user has any of the specified roles, field is required
                        if (requiredRoles.includes(currentUserRole)) {
                            return value && value.length > 0;
                        }
                        return true;
                    }, 'This field is required for your role.');
                    $("#groupServiceForm").validate({
                        debug: false,
                        rules: {
                            thumbnail: {
                                required: true,
                                maxFileSize: 5120
                            },
                            name: {
                                required: true,
                                minlength: 2
                            },
                            category_id: {
                                required: true
                            },
                            subcategory_id: {
                                required: true
                            },
                            'staff_ids[]': {
                                requiredForRole: {
                                    role: 'business'
                                }
                            },
                            physical_location: {
                                requiredIfChecked: {
                                    checkbox: '#onsite-secondary'
                                }
                            },
                            duration: {
                                required: true
                            },
                            price_per_slot: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            additional_cost: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            required_items: {
                                required: true,
                                maxlength: 1000
                            },
                            description: {
                                required: true,
                                maxlength: 1000
                            },
                            discount_recurring: {
                                required: true,
                                number: true,
                                min: 0,
                                max: 100
                            },
                            total_slots: {
                                required: true,
                                number: true,
                                min: 1
                            }
                        },
                        messages: {
                            thumbnail: {
                                required: "Please upload a thumbnail",
                                maxFileSize: "Image size must not exceed 5 MB"
                            },
                            name: {
                                required: "Service name is required",
                                minlength: "Service name must be at least 2 characters"
                            },
                            category_id: {
                                required: "Please select a category"
                            },
                            subcategory_id: {
                                required: "Please select a subcategory"
                            },
                            'staff_ids[]': {
                                requiredForRole: "Please assign at least one staff member to this service"
                            },
                            physical_location: {
                                requiredIfChecked: "Physical location is required when On-site is selected"
                            },
                            duration: {
                                required: "Please select service duration"
                            },
                            price_per_slot: {
                                required: "Service price is required",
                                number: "Please enter a valid price",
                                min: "Price cannot be negative"
                            },
                            additional_cost: {
                                required: "Additional cost is required",
                                number: "Please enter a valid additional cost",
                                min: "Additional cost cannot be negative"
                            },
                            required_items: {
                                required: "Required items is required",
                                maxlength: "Required items cannot exceed 1000 characters"
                            },
                            description: {
                                required: "Description is required",
                                maxlength: "Description cannot exceed 1000 characters"
                            },
                            discount_recurring: {
                                required: "Discount for recurring is required",
                                number: "Please enter a valid discount percentage",
                                min: "Discount percentage must be at least 0%",
                                max: "Discount percentage cannot exceed 100%"
                            },
                            total_slots: {
                                required: "Total slots is required",
                                number: "Please enter a valid number of slots",
                                min: "Total slots must be at least 1"
                            }
                        },
                        submitHandler: function(form) {
                            console.log('Form validation passed, submitting...');
                            // Update availability data
                            saveCurrentWeekData();

                            // Check if getSelectedAvailability function exists
                            if (typeof getSelectedAvailability === 'function') {
                                var selectedData = getSelectedAvailability();
                                // Add availability data to form
                                var availabilityInput = $('<input>').attr({
                                    type: 'hidden',
                                    name: 'availabilities_dates',
                                    value: JSON.stringify(selectedData)
                                });
                                $(form).find('input[name="availabilities_dates"]').remove();
                                $(form).append(availabilityInput);
                            }

                            form.submit();
                        },
                        invalidHandler: function(event, validator) {
                            console.log('Form validation failed. Errors:', validator.numberOfInvalids());
                        }
                    });

                    // Re-validate when checkboxes change to update conditional requirements
                    $('#onsite-secondary, #customer-location-secondary').on('change', function() {
                        // Clear previous validation errors for conditional fields
                        $('#pac-input, #radius, #traveltime, #servicecharges').removeClass(
                            'error');
                        $('.error[for="physical_location"], .error[for="radius"], .error[for="travel_time"], .error[for="service_charges"]')
                            .remove();

                        // Re-validate the form to apply new conditional rules
                        $("#groupServiceForm").valid();
                    });
                } else {
                    console.error('jQuery validate is not available');
                }
            }, 500);

            // Initialize data from JSON
            initializeDataFromJSON();
            updateWeekUI();
            updateJsonOutput();

            // Availability validation removed for now

            $(document).on("change", ".day-checkbox", function() {
                saveCurrentWeekData();
                updateWeekUI();
                updateJsonOutput(); // Update JSON when checkbox changes
            });

            // Validate time inputs when they change
            $(document).on("change", ".start-time, .end-time", function() {
                validateTimeInput(this);
                saveCurrentWeekData();
                updateJsonOutput(); // Update JSON when time changes
            });

            $("#prevWeek").click(function() {
                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#nextWeek").click(function() {
                saveCurrentWeekData();
                currentWeekIndex++;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            });

            $("#saveAvailability").click(function() {
                saveCurrentWeekData();
                updateJsonOutput(); // Final update of JSON
                alert("Availability Saved!");
            });

            // Recurring Radio Button Change
            $("input[name='recurring']").change(function() {
                const selected = $(this).val();
                saveCurrentWeekData();

                if (selected === "custom") {
                    $(".custom-weeks-input").show();
                } else {
                    $(".custom-weeks-input").hide();
                }

                if (selected === "custom") {
                    return false;
                }

                const repeatWeeks = parseInt(selected);

                if (repeatWeeks > 0) {
                    duplicateWeeks(repeatWeeks);
                    updateJsonOutput(); // Update JSON after duplication
                    alert(`Availability reset and duplicated for ${repeatWeeks} weeks total.`);
                }
            });

            // Handle Custom Weeks Input
            $("#customDone").click(function() {
                const customWeeks = parseInt($("#customWeeks").val());
                if (!isNaN(customWeeks) && customWeeks > 0) {
                    saveCurrentWeekData();
                    duplicateWeeks(customWeeks);
                    updateJsonOutput(); // Update JSON after custom duplication
                    alert(`Availability reset and duplicated for ${customWeeks} weeks total.`);
                    $(".custom-weeks-input").hide();
                    $("input[name='recurring']").prop('checked', false); // Uncheck radio buttons
                } else {
                    alert("Please enter a valid number of weeks.");
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/service/include/group.blade.php ENDPATH**/ ?>