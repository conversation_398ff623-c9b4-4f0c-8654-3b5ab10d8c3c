 <form action="<?php echo e(route('search_treatments')); ?>" method="GET">
     <div class="row row-gap-5">
         <div class="col-md-12">
             <label for="searchInput" class="visually-hidden">Search treatments and
                 venues</label>
             <div class="form-control home-input d-flex align-items-center">
                 <i class="fa-solid fa-magnifying-glass me-3"></i>
                 <input name="search" id="searchInput" type="search" class="w-100" value="<?php echo e(request('search')); ?>"
                     placeholder="All treatments and venues">
             </div>
         </div>

         <div class="col-md-3">
             <label for="locationInput" class="visually-hidden">Search by location</label>
             <div class="form-control home-input d-flex align-items-center w-100">
                 <i class="fa-solid fa-magnifying-glass me-3"></i>
                 <input name="location" id="pac-input" type="search" class="w-100" value="<?php echo e(request('location')); ?>"
                     placeholder="Current location">
             </div>
             <input type="hidden" name="lat" value="<?php echo e(request('lat')); ?>" id="latitude">
             <input type="hidden" name="lng" value="<?php echo e(request('lng')); ?>" id="longitude">
         </div>

         <div class="col-md-3">
             <label for="datePicker" class="visually-hidden">Select a date</label>
             <div class="form-control home-input d-flex align-items-center">
                 <span class="me-2"><i class="fa-regular fa-calendar"></i></span>
                 <input name="date" id="datePicker" type="date" placeholder="Any date" class="border-0 w-100"
                     value="<?php echo e(request('date')); ?>" />
             </div>
         </div>

         <div class="col-md-3">
             <label for="timePicker" class="visually-hidden">Select a time slot</label>
             <div class="form-control home-input d-flex align-items-center">
                 <span class="me-2"><i class="far fa-clock"></i></span>
                 <input name="time" id="timePicker" type="text" placeholder="Any timeslot" class="border-0 w-100"
                     value="<?php echo e(request('time') ? \Carbon\Carbon::parse(request('time'))->format('H:i') : ''); ?>" />
             </div>
         </div>

         <div class="col-md-3">
             <button type="submit" class="blue-button w-100">Search</button>
         </div>
     </div>
 </form>

<?php /**PATH D:\SALMAN\git\anders\resources\views/layouts/includes/search-box.blade.php ENDPATH**/ ?>