<div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-home">
    <div id="kt_app_content_container" class="app-container container-fluid">
        <section class="business-home-sec padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="semi_bold">Welcome, <PERSON></h6>
                        <p class="fs-14 normal">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                </div>
                <div class="row row-gap-5 mb-10 card-wrapper">
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Sales
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="642.39" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up" style="color: #059669;"></i>
                                        17.2%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        <?php echo $__env->make('svg.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-150px ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        From last month
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="574.34" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up" style="color: #059669;"></i>
                                        17.2%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        <?php echo $__env->make('svg.earning', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-150px ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Sales to date
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="350.4" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up" style="color: #059669;"></i>
                                        17.2%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        <?php echo $__env->make('svg.booking', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-75 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Number of Bookings
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="2935">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up" style="color: #059669;"></i>
                                        17.2%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="row row-gap-7">
                    <div class="col-md-6">
                        <div class="card-box">
                            <div class="d-flex justify-content-between align-items-center mb-3 px-2">
                                <p class="fs-18 semi_bold sora mb-0">Upcoming Bookings</p>
                                <a href="<?php echo e(url('bookings')); ?>" class="view-all-btn">View All<i
                                        class="fa-solid fa-chevron-right arrow"></i>
                                </a>
                            </div>
                            <div class="cards">
                                <div class="booking_card d-flex justify-content-between align-items-center flex-row">
                                    <div class="left d-flex align-items-center">
                                        <div class="date-box">
                                            <div class="day">24</div>
                                            <div class="month">Dec</div>
                                        </div>
                                        <div class="details">
                                            <div class="service-name">Hair Cut</div>
                                            <div class="service-time">
                                                Tue, 20 Aug 2024 2:45pm
                                                <span class="status complete">Complete</span>
                                            </div>
                                            <div class="customer-info fs-14 normal sora mt-2 light-black">
                                                <span class="opacity-6">For</span> Justin Dokidis <span
                                                    class="opacity-6">With</span> Cristofer Torff
                                            </div>
                                        </div>
                                    </div>
                                    <div class="price fs-20 sora semi_bold">$110</div>
                                </div>
                                <!-- Card 2 -->
                                <div class="booking_card d-flex justify-content-between align-items-center flex-row">
                                    <div class="left d-flex align-items-center">
                                        <div class="date-box">
                                            <div class="day">12</div>
                                            <div class="month">Jan</div>
                                        </div>
                                        <div class="details">
                                            <div class="service-name">Beard Trim</div>
                                            <div class="service-time">
                                                Wed, 15 Jan 2025 1:30pm
                                                <span class="status booked">Booked</span>
                                            </div>
                                            <div class="customer-info fs-14 normal sora mt-2 light-black">
                                                <span class="opacity-6">For</span> Mike Johnson <span
                                                    class="opacity-6">With</span> Sarah Lee
                                            </div>
                                        </div>
                                    </div>
                                    <div class="price fs-20 sora semi_bold">$50</div>
                                </div>
                                <!-- Card 3 -->
                                <div class="booking_card d-flex justify-content-between align-items-center flex-row">
                                    <div class="left d-flex align-items-center">
                                        <div class="date-box">
                                            <div class="day">05</div>
                                            <div class="month">Feb</div>
                                        </div>
                                        <div class="details">
                                            <div class="service-name">Facial</div>
                                            <div class="service-time">
                                                Fri, 05 Feb 2025 4:00pm
                                                <span class="status cancelled">Cancelled</span>
                                            </div>
                                            <div class="customer-info fs-14 normal sora mt-2 light-black">
                                                <span class="opacity-6">For</span> Alex Smith <span
                                                    class="opacity-6">With</span> Daniel Roy
                                            </div>
                                        </div>
                                    </div>
                                    <div class="price fs-20 sora semi_bold">$80</div>
                                </div>
                                <!-- Card 4 -->
                                <div class="booking_card d-flex justify-content-between align-items-center flex-row">
                                    <div class="left d-flex align-items-center">
                                        <div class="date-box">
                                            <div class="day">18</div>
                                            <div class="month">Mar</div>
                                        </div>
                                        <div class="details">
                                            <div class="service-name">Massage</div>
                                            <div class="service-time">
                                                Mon, 18 Mar 2025 11:00am
                                                <span class="status complete">Complete</span>
                                            </div>
                                            <div class="customer-info fs-14 normal sora mt-2 light-black">
                                                <span class="opacity-6">For</span> Nancy Drew <span class="opacity-6">
                                                    With</span> Chris Evans
                                            </div>
                                        </div>
                                    </div>
                                    <div class="price fs-20 sora semi_bold">$120</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card-box">
                            <div class="row">
                                <div class="col-md-12 d-flex justify-content-between">
                                    <div class="weekly-content">
                                        <p class="fs-18 semi_bold sora">Weekly overview</p>
                                        <p class="fs-14 normal sora light-black">Last 7 days</p>
                                        <p class="fs-32 normal sora ">$636.00</p>
                                    </div>
                                    <div class="select-wrapper ">
                                        <select class="normal weekly-dropdown">
                                            <option value="pre-week">Pre Week</option>
                                            <option value="this-week">This Week</option>
                                            <option value="next-week">Next Week</option>
                                            <option value="custom">Custom</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="monthly-customers-container" class="pt-3">
                                <canvas id="line_chart" class="mh-400px"> </canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card-box ">
                            <div class="d-flex justify-content-between align-items-center mb-3 px-2">
                                <p class="fs-18 semi_bold sora mb-0">Booked Time</p>
                                <a href="<?php echo e(url('booking')); ?>" class="view-all-btn ">View All<i
                                        class="fa-solid fa-chevron-right arrow"></i>
                                </a>
                            </div>
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Monday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-blue" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Tuesday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-light-green" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Wednesday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-purple" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Thursday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-orange" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>                
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Friday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-blue" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Saturday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-light-green" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="book-time-card mb-5">
                                <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                    <!-- Day -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        <strong>Sunday</strong>
                                    </div>

                                    <!-- Time -->
                                    <div class="semi_bold fs-14 light-black sora opacity-8"
                                        style="min-width: 0; flex: 1;">
                                        2:00 pm - 5:00 pm
                                    </div>

                                    <!-- Progress with percent -->
                                    <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-purple" style="width: 35%;"></div>
                                        </div>
                                        <span class="semi_bold fs-14 sora black">35%</span>
                                    </div>
                                </div>
                            </div>

                        
                        </div>

                    </div>
                    <div class="col-md-6">
                        <div class="card-box">
                            <div class="notification-bg">
                                <div class="d-flex justify-content-between align-items-center mb-3 px-2">
                                    <p class="fs-18 semi_bold sora mb-0">Recent Notifications</p>

                                    <a href="<?php echo e(url('notification')); ?>" class="view-all-btn ">View All<i
                                        class="fa-solid fa-chevron-right arrow"></i>
                                </a>
                                </div>
                                <div class="scrollbar px-2">
                                    <div class="row">
                                        <?php for($i = 0; $i < 10; $i++): ?>
                                            <div class="col-lg-12">
                                                <div class="noti-content d-flex flex-row gap-5 align-items-center">
                                                    <div class="card-header profile-img">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/noti-profile.png"
                                                            class='rounded-pill' alt="profile">
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="noti-heading pt-5 d-flex justify-content-between">
                                                            <p class="fs-15 semi_bold sora mb-2">Charlie Culhane</p>
                                                            <p class="dark-cool-gray fs-12 normal">1 hour</p>
                                                        </div>
                                                        <div class="mail-massage">
                                                            <p class="fs-14 normal dark-cool-gray line-clamp-1">Lorem ipsum
                                                                dolor sit amet consectetur. Sit vehicula integer vel egestas
                                                                eros tellus diam nibh porttitor. </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<?php echo $__env->make('dashboard.templates.modal.add-service-details-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>



<?php $__env->startPush('js'); ?>
    
    <script>
        var ctx3 = document.getElementById('line_chart');
        var darkBlue = '#283B63E5'; // Dark Blue
        var transparent = '#00FFFFFF'; // Dark Blue

        // Define fonts
        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');
        // Chart data
        const data3 = {
            labels: ['Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun', 'Mon', 'Tue'],
            datasets: [{
                label: 'Total Sale',
                data: [150, 200, 200, 120, 70, 280, 220, 300],
                borderColor: '#0095FF ',
                backgroundColor: '#0095FF',
                fill: false,
                pointRadius: 4,
                pointHoverRadius: 2,
                lineTension: 0

            }]
        };
        // Chart config
        const config3 = {
            type: 'line',
            data: data3,
            options: {
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        align: 'start',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20,
                            boxWidth: 8,
                            boxHeight: 8,
                            color: '#0095FF',
                        }
                    },
                    title: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: function () {
                                return '';
                            },
                        }
                    }
                },
                responsive: true,
                interaction: {
                    intersect: false,
                },

                scales: {
                    x: {
                        stacked: true,
                        grid: {
                            display: true,
                        },
                    },
                    y: {
                        stacked: true,
                        grid: {
                            display: true,
                        },
                        min: 0,
                        max: 500,
                        ticks: {
                            display: true,
                            stepSize: 100,
                            maxTicksLimit: 7,
                            callback: function (value) {
                                return '$' + value;
                            }

                        }
                    }
                }
            },
            defaults: {
                global: {
                    defaultFont: fontFamily
                }
            }
        };
        // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/
        var myChart3 = new Chart(ctx3, config3);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/templates/index/business_index.blade.php ENDPATH**/ ?>