<!-- Filters Modal -->
<div class="modal fade add-services-modal" id="add-booking" tabindex="-1" aria-labelledby="addBooking"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold" id="filtersModalLabel">Add Booking</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="#" class="form-add-services">
                    <div class="row row-gap-8">
                        <div class="col-md-12">
                            <label for="clientName" class="form-label form-input-labels">Client Name</label>
                            <input type="text" class="form-control form-inputs-field" name="clientName" id="clientName"
                                placeholder="Enter client name">
                        </div>
                        <div class="col-md-12">
                            <label for="clientEmail" class="form-label form-input-labels">Email</label>
                            <input type="email" class="form-control form-inputs-field" name="clientEmail"
                                id="clientEmail" placeholder="Enter client email">

                        </div>
                        <div class="col-md-12">
                            <label for="phone" class="form-label form-input-label">Phone Number</label>
                            <div class="form-control form-inputs-field">
                                <input type="tel" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <!-- service name -->
                            <!-- <div class="service-details p-3"> -->
                            <label for="service" class="form-label form-input-labels">Select Service</label>
                            <select class="form-select form-select-field" id="service" name="service"
                                data-control="select2" data-placeholder="Select">
                                <option></option>
                                <option value="hair-cut" data-price="$17.84" data-duration="15 mins">Hair Cut
                                </option>
                                <option value="shave" data-price="$10.50" data-duration="10 mins">Shave</option>
                            </select>
                            <!-- </div> -->
                        </div>
                        <div class="col-md-12">
                            <label for="addItems" class="form-label form-input-labels">Add Items</label>
                            <!--begin::Repeater-->
                            <div id="kt_docs_repeater_basic">
                                <!--begin::Form group-->
                                <div class="form-group mt-5">
                                    <div data-repeater-list="kt_docs_repeater_basic">
                                        <div data-repeater-item class="mt-2">
                                            <div class="form-group row row-gap-5">
                                                <div class="col-md-6">
                                                    <label class="form-label" for="itemName">Item Name</label>
                                                    <input type="text" class="form-control form-inputs-field"
                                                        placeholder="Enter Item Name" name="itemName" id="itemName" />
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label" for="itemPrice">Item Price</label>
                                                    <input type="text" class="form-control form-inputs-field"
                                                        placeholder="Enter Item Price" name="itemPrice"
                                                        id="itemPrice" />
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="d-flex justify-content-end">
                                                        <a href="javascript:;" data-repeater-delete
                                                            class="drop-btn delete-btn btn btn-outline-danger  py-2 px-3 text-center"><i
                                                                class="bi bi-trash p-0 red"></i>
                                                            Delete
                                                        </a>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--end::Form group-->

                                <!--begin::Form group-->
                                <div class="form-group">
                                    <a href="javascript:;" data-repeater-create class="add-btn">
                                        <i class="ki-duotone ki-plus"></i>
                                        Add
                                    </a>
                                </div>
                                <!--end::Form group-->
                            </div>
                            <!--end::Repeater-->
                        </div>

                        <!-- select professional -->
                        <div class="col-md-12">

                            <div class="service-details d-flex justify-content-between p-3 pb-5">
                                <div class="service-name w-100">
                                    <label class="form-label form-input-labels pb-3">Select Professional</label>
                                    <div class="professional-checkbox row row-gap-5">
                                        <?php
                                            $images = [
                                                'family1.png',
                                                'family2.png',
                                                'family3.png',
                                                'family4.png',
                                                'family4.png',
                                                'family4.png',
                                                'family4.png',
                                            ];
                                        ?>
                                        <?php for($i = 0; $i < 6; $i++): ?>
                                            <div class="col-md-4">
                                                <label class="category-checkbox">
                                                    <input type="radio" name="category" value="Booking for myself" checked>
                                                    <div class="card top-rated-card family-cards">
                                                        <div
                                                            class="card-header border-0 p-0 position-relative justify-content-center align-items-center">
                                                            <img src="<?php echo e(asset('website/assets/images/' . $images[$i])); ?>"
                                                                class="h-90px w-90px object-fit-contain rounded-pill top-rated-image"
                                                                alt="card-image">
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p
                                                                class=" professional-name fs-16 semi_bold black text-center m-0 w-100 shadow-none ">
                                                                Roger Press</p>
                                                            <p
                                                                class=" professional-profession fs-14 regular light-black opacity-6 text-center  m-0 w-100 shadow-none">
                                                                Father</p>
                                                        </div>
                                                    </div>
                                            </div>
                                        <?php endfor; ?>

                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- date nd time -->
                        <div class="col-md-12">
                            <div class="calendar service-details p-3 pb-5">
                                <label class="form-label form-input-labels pb-3">Select Date & Time</label>
                                <div class="date-header">
                                    <div>

                                        <select id="monthSelect" class="border-0 fs-14 semi-bold black outline-0"
                                            onchange="loadDays()"></select>


                                    </div>
                                    <div>
                                        <button type="button" onclick="changeWeek(-1)"><i
                                                class="fa-solid fa-chevron-left"></i></button>
                                        <button type="button" onclick="changeWeek(1)"><i
                                                class="fa-solid fa-chevron-right"></i></button>
                                    </div>
                                </div>

                                <label class="category-checkbox">
                                    <input type="radio" name="category" value="date-days" checked>
                                    <div id="dateDays"
                                        class="date-days d-flex gap-2 justify-content-center flex-nowrap overflow-auto">
                                    </div>
                                </label>


                                <label class="form-label form-input-labels py-5">Available Time Slots</label>
                                <label class="category-checkbox">
                                    <input type="radio" name="category" value="time slots" checked>
                                    <div class="row">
                                        <div id="timeSlots"
                                            class="time-slots d-flex justify-content-between flex-wrap gap-3"></div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- comments -->
                        <div class="col-md-12">
                            <div class="service-details d-flex justify-content-between p-3 pb-5">
                                <div class="service-name w-100">
                                    <label class="form-label form-input-labels pb-3">Comments</label>
                                    <textarea class="form-control" rows="5" placeholder="Write Comments"></textarea>
                                    <p class="form-label form-input-labels py-2 light-black">Comment for any special
                                        instructions.</p>

                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="cancel-btn " data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="save-btn " data-bs-dismiss="modal">Add Booking</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function () {
            $('input[name="loc"]').on('change', function () {
                var selectedValue = $('input[name="loc"]:checked').val();

                if (selectedValue === "Providers location") {
                    $('#providers-loc').show();
                    $('#home-loc').hide();
                } else if (selectedValue === "Your Home/Office") {
                    $('#providers-loc').hide();
                    $('#home-loc').show();
                }
            });
        });
    </script>


    <script>
        const timeOptions = ["08:30 AM", "09:00 AM", "09:15 AM", "09:45 AM", "10:00 AM", "10:30 AM", "11:30 AM", "12:00 PM", "12:45 PM", "01:15 PM", "01:30 PM", "02:00 PM"];
        let currentDate = new Date(2025, 4, 12); // example: May 12, 2025

        function generateMonthOptions() {
            const monthSelect = document.getElementById("monthSelect");
            monthSelect.innerHTML = ""; // clear existing options

            const months = [
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            ];

            const startMonth = currentDate.getMonth();
            const startYear = currentDate.getFullYear();

            for (let i = 0; i < 12; i++) {
                const tempDate = new Date(startYear, startMonth + i, 1);
                const option = document.createElement("option");
                option.value = tempDate.getMonth();
                option.setAttribute("data-year", tempDate.getFullYear());
                option.textContent = `${months[tempDate.getMonth()]} ${tempDate.getFullYear()}`;

                if (
                    tempDate.getMonth() === currentDate.getMonth() &&
                    tempDate.getFullYear() === currentDate.getFullYear()
                ) {
                    option.selected = true;
                }

                monthSelect.appendChild(option);
            }
        }

        function loadDays() {
            const monthSelect = document.getElementById("monthSelect");
            const selectedMonth = parseInt(monthSelect.value);
            const selectedYear = parseInt(monthSelect.selectedOptions[0].getAttribute("data-year"));

            const start = new Date(selectedYear, selectedMonth, currentDate.getDate());

            const daysDiv = document.getElementById("dateDays");
            daysDiv.innerHTML = "";

            for (let i = 0; i < 7; i++) {
                const day = new Date(start);
                day.setDate(day.getDate() + i);

                const label = document.createElement("label");
                label.className = "category-checkbox";

                const input = document.createElement("input");
                input.type = "radio";
                input.name = "day";
                input.value = day.toDateString();
                if (i === 0) input.checked = true;

                const div = document.createElement("div");
                div.className = "date-box top-rated-card d-flex flex-column gap-2";
                div.innerHTML = `
                                                                    <p class="calender-date w-100 text-center m-0 border-0 shadow-none px-0">${day.getDate()}</p>
                                                                    <p class="calender-day w-100 text-center m-0 border-0 shadow-none px-0">${day.toLocaleDateString('en-US', { weekday: 'short' })}</p>
                                                                  `;

                input.onclick = () => selectDate(div, day);
                if (i === 0) div.classList.add("selected");

                label.appendChild(input);
                label.appendChild(div);
                daysDiv.appendChild(label);
            }

            loadTimeSlots();
        }

        function changeWeek(dir) {
            currentDate.setDate(currentDate.getDate() + (dir * 7));
            generateMonthOptions(); // to refresh month/year in dropdown
            loadDays();
        }

        function selectDate(el, date) {
            document.querySelectorAll(".date-box").forEach(d => d.classList.remove("selected"));
            el.classList.add("selected");
            currentDate = date;
            loadTimeSlots();
        }

        function loadTimeSlots() {
            const slotContainer = document.getElementById("timeSlots");
            slotContainer.innerHTML = "";

            timeOptions.forEach((t, index) => {
                const label = document.createElement("label");
                label.className = "category-checkbox w-25";

                const input = document.createElement("input");
                input.type = "radio";
                input.name = "time";
                input.value = t;
                if (index === 0) input.checked = true;

                const slotDiv = document.createElement("div");
                slotDiv.className = "top-rated-card time-slots px-7 py-2";
                slotDiv.innerText = t;

                input.onclick = () => {
                    document.querySelectorAll(".top-rated-card").forEach(s => s.classList.remove("selected"));
                    slotDiv.classList.add("selected");
                };

                label.appendChild(input);
                label.appendChild(slotDiv);
                slotContainer.appendChild(label);
            });
        }

        // 🔄 INIT
        generateMonthOptions();
        loadDays();
    </script>
    <script>
        $(document).ready(function () {
            $('#service').select2({
                placeholder: "Select",
                templateResult: formatServiceOption,
                templateSelection: formatServiceSelection,
                escapeMarkup: function (markup) { return markup; } // Allow HTML rendering
            });

            function formatServiceOption(option) {
                if (!option.id) return option.text;

                const price = $(option.element).data('price');
                const duration = $(option.element).data('duration');

                return `
                            <div style="display: flex; flex-direction: column;">
                              <div style="font-weight: 500; font-size:16px;color:black;">${option.text}</div>
                              <div style="margin-top: 2px;">
                                <span style="color: #000; font-size: 14px; font-weight: 600;">${price}</span>
                                <span style="color: #363636; font-size: 14px; margin-left: 8px; opacity:0.6;"> •  ${duration}</span>
                              </div>
                            </div>
                          `;
            }

            function formatServiceSelection(option) {
                if (!option.id) return option.text;

                const price = $(option.element).data('price');
                const duration = $(option.element).data('duration');

                return `
                            <div style="display: flex; flex-direction: column;">
                              <div style="font-weight: 500; font-size:16px;color:black;">${option.text}</div>
                              <div style="margin-top: 2px;">
                                <span style="color: #000; font-size: 14px; font-weight: 600;">${price}</span>
                                <span style="color: #363636; font-size: 14px; margin-left: 8px; opacity:0.6;"> • ${duration}</span>
                              </div>
                            </div>
                          `;
            }
        });
    </script>

<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/templates/modal/add-booking-modal.blade.php ENDPATH**/ ?>