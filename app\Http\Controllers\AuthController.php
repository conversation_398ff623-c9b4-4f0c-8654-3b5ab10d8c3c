<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Certification;
use App\Models\Holiday;
use App\Models\Profile;
use App\Models\ProfessionalRegistrationProgress;
use App\Models\Service;
use App\Models\User;
use App\Models\UserCertificate;
use App\Models\UserHoliday;
use App\Models\UserOpeningHour;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    function set_password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'password' => ['required'],
            'scenario' => 'sometimes|in:login,register', // Optional parameter to indicate scenario
            'user_type' => 'sometimes|in:customer,professional', // User type for role validation
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }

        $user = User::where('email', $request->email)->first();

        if ($user) {
            // User exists - this is a login scenario
            if (!Hash::check($request->password, $user->password)) {
                return api_response(false, "Invalid password");
            }

            // Check if user has the correct role for the selected user type
            $userRole = $user->getRoleNames()->first();
            $selectedUserType = $request->user_type;
            if (in_array($userRole, ["individual", "business", "professional"]) || $selectedUserType == "professional") {
            } elseif ($userRole == "customer" && $selectedUserType == "customer") {
            } else {
                return api_response(false, "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            }

            // Check if user is professional and has completed registration
            if (!$user->hasRole('customer')) {
                if ($user->registration_completed == 1) {
                    // Check if user is approved
                    if ($user->approval == 0) {
                        return api_response(false, "Your account is not yet approved. Please wait for approval.");
                    }
                }
            }

            // Check if user is active
            if ($user->status == 0) {
                return api_response(false, "Your account is not active. Please contact support.");
            }

            // Update online status
            $user->update([
                'is_online' => true,
                'online_at' => now()
            ]);

            // Broadcast user online status
            broadcast(new \App\Events\UserOnline($user->id));

            // Login the user
            Auth::login($user);

            return response()->json([
                'status' => true,
                'message' => "Login successful",
                'role' => $userRole,
                'scenario' => 'login'
            ]);
        } else {
            // User doesn't exist - this should not happen in normal flow
            // But we'll handle it as registration scenario
            return api_response(false, "User not found. Please complete email verification first.");
        }
    }

    function registerUserType($user_type)
    {
        if (auth()->user()->registration_completed == 1) {
            return redirect()->route('dashboard');
        }
        $role = auth()->user()->roles->first()->name ?? null;
        if ($role && $role == $user_type) {
            $user = auth()->user();
            if ($user->hasRole("customer")) {
                if (auth()->user()->profile) {
                    return redirect()->route('dashboard');
                }
            }
            $categories = Category::where("status", 1)->get();
            $services = Service::where("status", 1)->get();
            $certifications = Certification::all();
            $holidays = Holiday::where("status", 1)->get();
            return view("auth.register." . $user_type, compact("categories", "certifications", 'holidays', 'services'));
        } else {
            abort(403, 'Unauthorized action.');
        }
    }

    function registerCustomer(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'fullname' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'password' => ['required', 'regex:' . config('constant.password_regex'), 'min:' . config('constant.password_min'), 'max:' . config('constant.password_max')],
            'phone' => ['required', 'max:' . config('constant.phone_length')],
            'services' => 'required',
            'location' => 'required|string',
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
        ], [
            'avatar.required' => 'Please upload your profile image.',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'fullname.required' => 'Please enter your full name.',
            'fullname.regex' => $messagesConfig['input']['regex'],
            'fullname.max' => $messagesConfig['input']['max'],
            'password.required' => $messagesConfig['password']['required'],
            'password.regex' => $messagesConfig['password']['regex'],
            'password.min' => $messagesConfig['password']['min'],
            'password.max' => $messagesConfig['password']['max'],
            'phone.required' => 'Please enter your phone number.',
            'phone.max' => $messagesConfig['phone']['max'],
            'services.required' => 'Please select at least one service.',
            'location.required' => 'Please select your location.',
            'lat.required' => 'Please select your location.',
            'lat.numeric' => 'Please select your location.',
            'lng.required' => 'Please select your location.',
            'lng.numeric' => 'Please select your location.',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->with([
                    'error' => 'Validation Error',
                    'message' => $validator->errors()->first(),
                    'type' => 'error',
                ])
                ->withInput();
        }
        $user = User::find(auth()->id());
        $user->name = $request->fullname;
        $user->password = Hash::make($request->password);
        $user->registration_completed = 1;
        $user->save();
        $profile = $user->profile;
        if ($profile == null) {
            $profile = new Profile();
            $profile->phone = $request->phone;
            $profile->location = $request->location;
            $profile->lat = $request->lat;
            $profile->lng = $request->lng;
            if (isset($request->avatar)) {
                $profile->pic = $this->storeImage("user-image", $request->file('avatar'));
            } else {
                $profile->pic = 'no_avatar.jpg';
            }
            $profile->save();
        }
        $profile->user_id = $user->id;
        $profile->save();
        if ($request->has('services')) {
            $user->service_preferences()->sync($request->services);
        }
        $admin = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['admin']);
        })->first();
        $this->user_notification($admin->id, 'New Customer Registered', 'A new customer '.$user->name.' has registered on the platform.');
        return redirect("/")->with([
            'title' => 'Done',
            'message' => 'You have been registered successfully',
            'type' => 'success',
        ]);
    }

    /**
     * Save step data for professional registration
     */
    public function saveStepData(Request $request)
    {
        // return $request->all();
        $user = auth()->user();
        $step = $request->input('step');
        // Get or create progress record
        $progress = ProfessionalRegistrationProgress::firstOrCreate(
            ['user_id' => $user->id],
            ['current_step' => 1]
        );

        // Handle file uploads first
        $stepData = $this->handleFileUploads($request, $step);

        // Validate and save step data
        $validatedData = $this->validateStepData($request, $step);
        if (isset($validatedData["error"])) {
            return response()->json(['success' => false, 'message' => $validatedData["error"]]);
        }

        // For step 5, process the banner image immediately to avoid storing large base64 data
        if ($step == 5 && isset($validatedData['banner_image']) && !empty($validatedData['banner_image'])) {
            try {
                $validatedData['banner_image'] = $this->handleCroppedImage($validatedData['banner_image']); // Store only filename, not base64
            } catch (\Exception $e) {
                Log::error('Failed to process banner image: ' . $e->getMessage());
                return response()->json(['success' => false, 'message' => 'Failed to process banner image']);
            }
        }

        // Merge file upload data with validated data
        $stepData = array_merge($validatedData, $stepData);

        // Save step data with error handling for large data
        try {
            $progress->setStepData($step, $stepData);
        } catch (\Exception $e) {
            Log::error('Failed to save step data: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to save step data. Please try again.']);
        }

        // Also save to profile for immediate use
        $this->saveToProfile($user, $step, $stepData);

        // If this is step 5 (final step), return success for AJAX and let frontend handle redirect
        if ($step == 5) {
            $admin = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['admin']);
            })->first();
            $this->user_notification($admin->id, 'New Professional Registered', 'A new professional '.$user->name.' has registered on the platform.');
            Auth::logout($user);
            return redirect('/')->with([
                'title' => 'Done',
                'message' => 'You have been registered successfully, your account will be approved shortly ',
                'type' => 'success',
            ]);
        }
        return response()->json(['success' => true, 'current_step' => $progress->current_step]);
    }

    /**
     * Get current progress for professional registration
     */
    public function getProgress(Request $request)
    {
        $user = auth()->user();
        $progress = ProfessionalRegistrationProgress::where('user_id', $user->id)->first();
        if ($progress) {
            return response()->json([
                'success' => true,
                'current_step' => $progress->current_step,
                'completed_steps' => $progress->getCompletedStepsCount()
            ]);
        }
        return response()->json(['success' => true, 'current_step' => 1]);
    }

    /**
     * Validate step data based on step number
     */
    private function validateStepData(Request $request, $step)
    {
        if ($step == 3) {
            return $this->validateStep3Data($request);
        }

        if ($step == 4) {
            return $this->validateStep4Data($request);
        }

        $rules = $this->getValidationRules($step);
        $messages = $this->getValidationMessages($step);
        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return ['error' => $validator->errors()->first()];
            // return false;
        }
        return $request->only(array_keys($rules));
    }

    private function validateStep3Data(Request $request)
    {
        $rules = [
            'product_certifications' => 'required|array|min:1',
            'certificates' => 'nullable|array',
        ];

        $messages = [
            'product_certifications.required' => 'Please select at least one product certification.',
            'product_certifications.array' => 'Invalid product certifications selection.',
            'product_certifications.min' => 'Please select at least one product certification.',
        ];

        // Add dynamic validation for certificates
        if ($request->has('certificates')) {
            foreach ($request->input('certificates', []) as $index => $certificate) {
                // Check if any field is filled for this certificate
                $hasAnyField = !empty($certificate['title']) ||
                    !empty($certificate['issued_by']) ||
                    !empty($certificate['issued_date']) ||
                    !empty($certificate['end_date']);

                if ($hasAnyField) {
                    // If any field is filled, all fields become required
                    $rules["certificates.{$index}.title"] = 'required|string|max:255';
                    $rules["certificates.{$index}.issued_by"] = 'required|string|max:255';
                    $rules["certificates.{$index}.issued_date"] = 'required|date';
                    $rules["certificates.{$index}.end_date"] = 'required|date|after:certificates.' . $index . '.issued_date';

                    // Image or exception logic: either upload image OR check exception with reason
                    $hasException = !empty($certificate['exception']);
                    $hasImage = $request->hasFile("certificates.{$index}.image") || !empty($certificate['old_image']);

                    if ($hasException) {
                        // If exception is checked, reason is required, image is optional
                        $rules["certificates.{$index}.exception_reason"] = 'required|string|max:500';
                        $messages["certificates.{$index}.exception_reason.required"] = "Reason for exception is required when certificate exception is checked for certificate #" . ($index + 1) . ".";
                    } else {
                        // If no exception, image is required
                        if (!$hasImage) {
                            $rules["certificates.{$index}.image"] = 'required|mimes:jpeg,png,jpg|max:2048';
                            $messages["certificates.{$index}.image.required"] = "Certificate image is required for certificate #" . ($index + 1) . " or check certificate exception.";
                        }
                    }

                    // If image is provided, validate it
                    if ($request->hasFile("certificates.{$index}.image")) {
                        $rules["certificates.{$index}.image"] = 'mimes:jpeg,png,jpg|max:2048';
                    }

                    // Add custom messages for required fields
                    $messages["certificates.{$index}.title.required"] = "Certificate title is required for certificate #" . ($index + 1) . ".";
                    $messages["certificates.{$index}.issued_by.required"] = "Issued by is required for certificate #" . ($index + 1) . ".";
                    $messages["certificates.{$index}.issued_date.required"] = "Issued date is required for certificate #" . ($index + 1) . ".";
                    $messages["certificates.{$index}.end_date.required"] = "Expiry date is required for certificate #" . ($index + 1) . ".";
                    $messages["certificates.{$index}.end_date.after"] = "Expiry date must be after issued date for certificate #" . ($index + 1) . ".";
                }
            }
        }

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return ['error' => $validator->errors()->first()];
        }
        return $request->only(array_keys($rules));
    }

    private function validateStep4Data(Request $request)
    {
        $rules = [
            'availability' => 'required|array',
            'holidays' => 'nullable|array',
            'custom_holidays' => 'nullable|array',
        ];

        $messages = [
            'availability.required' => 'Please set your availability.',
            'availability.array' => 'Invalid availability data.',
            'holidays.array' => 'Invalid holidays data.',
            'custom_holidays.array' => 'Invalid custom holidays data.',
        ];

        // Custom validation for time ranges
        $availability = $request->input('availability', []);
        foreach ($availability as $index => $day) {
            if (isset($day['day']) && isset($day['start']) && isset($day['end'])) {
                $dayName = $day['day'];
                $startTime = $day['start'];
                $endTime = $day['end'];

                // Convert 12-hour format to 24-hour format for comparison
                try {
                    $startTime24 = \Carbon\Carbon::createFromFormat('h:i A', $startTime)->format('H:i');
                    $endTime24 = \Carbon\Carbon::createFromFormat('h:i A', $endTime)->format('H:i');

                    if ($startTime24 >= $endTime24) {
                        return ['error' => "End time must be after start time for {$dayName}."];
                    }
                } catch (\Exception $e) {
                    return ['error' => "Invalid time format for {$dayName}. Please use proper time format."];
                }
            }
        }

        // Validate holidays time ranges
        $holidays = $request->input('holidays', []);
        foreach ($holidays as $index => $holiday) {
            if (isset($holiday['holiday_id'])) { // Holiday is checked
                $holidayName = $holiday['name'] ?? "Holiday";
                $startTime = $holiday['start_time'] ?? '';
                $endTime = $holiday['end_time'] ?? '';

                // If holiday is checked, both start and end times are required
                if (empty($startTime) || empty($endTime)) {
                    return ['error' => "Please provide both start and end time for {$holidayName}."];
                }

                try {
                    $startTime24 = \Carbon\Carbon::createFromFormat('h:i A', $startTime)->format('H:i');
                    $endTime24 = \Carbon\Carbon::createFromFormat('h:i A', $endTime)->format('H:i');

                    if ($startTime24 >= $endTime24) {
                        return ['error' => "End time must be after start time for {$holidayName}."];
                    }
                } catch (\Exception $e) {
                    return ['error' => "Invalid time format for {$holidayName}. Please use proper time format."];
                }
            }
        }

        // Validate custom holidays time ranges
        $customHolidays = $request->input('custom_holidays', []);

        foreach ($customHolidays as $index => $value) {
            // Only validate if this is a checkbox value (string) indicating the holiday is checked
            if (is_string($value)) {
                // Look for the corresponding data array
                $customHolidayData = $customHolidays[$index] ?? null;

                // If we have data for this checked holiday, validate it
                if (is_array($customHolidayData)) {
                    $holidayName = $customHolidayData['name'] ?? "Custom Holiday";
                    $startTime = $customHolidayData['start_time'] ?? '';
                    $endTime = $customHolidayData['end_time'] ?? '';

                    // If custom holiday is checked, both start and end times are required
                    if (empty($startTime) || empty($endTime)) {
                        return ['error' => "Please provide both start and end time for {$holidayName}."];
                    }

                    try {
                        $startTime24 = \Carbon\Carbon::createFromFormat('h:i A', $startTime)->format('H:i');
                        $endTime24 = \Carbon\Carbon::createFromFormat('h:i A', $endTime)->format('H:i');

                        if ($startTime24 >= $endTime24) {
                            return ['error' => "End time must be after start time for {$holidayName}."];
                        }
                    } catch (\Exception $e) {
                        return ['error' => "Invalid time format for {$holidayName}. Please use proper time format."];
                    }
                }
            }
        }

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return ['error' => $validator->errors()->first()];
        }
        return $request->only(array_keys($rules));
    }

    /**
     * Get validation rules for each step
     */
    private function getValidationRules($step)
    {
        switch ($step) {
            case 1:
                return [
                    'avatar' => ['required', 'mimes:' . config('constant.image_mimes') . ', max:' . config('constant.image_size')],
                    'full_name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
                    'company_name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
                    'phone' => ['required', 'regex:' . config('constant.phone_regex'), 'max:' . config('constant.phone_length')],
                    'website' => ['nullable', 'regex:' . config('constant.url_regex')],
                    'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
                    'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
                    'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
                    'location' => 'required|string',
                    'lat' => 'required|numeric',
                    'lng' => 'required|numeric',
                    'location_service' => 'required|integer|min:1',
                    'company_id' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
                    'vat_number' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
                ];
            case 2:
                return [
                    'categories' => 'required|array|min:1|max:3',
                    'subcategories' => 'required',
                ];
            case 3:
                return [
                    'product_certifications' => 'required|array|min:1',
                    'certificates' => 'nullable|array',
                ];
            case 4:
                return [
                    'availability' => 'required|array',
                    'holidays' => 'nullable|array',
                    'custom_holidays' => 'nullable|array',
                ];
            case 5:
                return [
                    'banner_image' => 'required|string',
                    // 'gallery_images' => 'nullable|array',
                ];
            default:
                return [];
        }
    }

    /**
     * Get validation messages for each step
     */
    private function getValidationMessages($step)
    {
        switch ($step) {
            case 1:
                return [
                    'avatar.required' => 'Please upload a profile picture.',
                    'avatar.mimes' => 'Profile picture must be an image file (jpeg, jpg, png, gif, webp).',
                    'avatar.max' => 'Profile picture size must not exceed 5MB.',
                    'full_name.required' => 'Please enter your full name.',
                    'full_name.regex' => 'Full name can only contain letters, numbers, and spaces.',
                    'full_name.max' => 'Full name must not exceed 255 characters.',
                    'company_name.required' => 'Please enter your company name.',
                    'company_name.regex' => 'Company name can only contain letters, numbers, and spaces.',
                    'company_name.max' => 'Company name must not exceed 255 characters.',
                    'phone.required' => 'Please enter your phone number.',
                    'phone.regex' => 'Please enter a valid phone number.',
                    'phone.max' => 'Phone number must not exceed 20 characters.',
                    'website.regex' => 'Please enter a valid website URL.',
                    'facebook.regex' => 'Please enter a valid Facebook URL.',
                    'instagram.regex' => 'Please enter a valid Instagram URL.',
                    'tiktok.regex' => 'Please enter a valid TikTok URL.',
                    'location.required' => 'Please select your location.',
                    'location_service.required' => 'Please select your service area.',
                    'location_service.min' => 'Please select a valid service area.',
                    'company_id.required' => 'Please enter your company ID.',
                    'company_id.regex' => 'Company ID can only contain letters, numbers, and spaces.',
                    'company_id.max' => 'Company ID must not exceed 255 characters.',
                    'vat_number.required' => 'Please enter your VAT number.',
                    'vat_number.regex' => 'VAT number can only contain letters, numbers, and spaces.',
                    'vat_number.max' => 'VAT number must not exceed 255 characters.',
                    'vat_number.max' => 'VAT number must not exceed 255 characters.',
                    'lat.required' => 'Please select your location.',
                    'lat.numeric' => 'Please select your location.',
                    'lng.required' => 'Please select your location.',
                    'lng.numeric' => 'Please select your location.',
                ];
            case 2:
                return [
                    'categories.required' => 'Please select at least one category.',
                    'categories.array' => 'Invalid categories selection.',
                    'categories.min' => 'Please select at least one category.',
                    'categories.max' => 'You can select maximum 3 categories.',
                    'subcategories.required' => 'Please select at least one subcategory.',
                    'subcategories.array' => 'Invalid subcategories selection.',
                    'subcategories.min' => 'Please select at least one subcategory.',
                ];
            case 3:
                return [
                    'product_certifications.required' => 'Please select at least one product certification.',
                    'product_certifications.array' => 'Invalid product certifications selection.',
                    'product_certifications.min' => 'Please select at least one product certification.',
                    'certificates.array' => 'Invalid certificates data.',
                ];
            case 4:
                return [
                    'availability.required' => 'Please set your availability.',
                    'availability.array' => 'Invalid availability data.',
                    'holidays.array' => 'Invalid holidays data.',
                    'custom_holidays.array' => 'Invalid custom holidays data.',
                ];
            case 5:
                return [
                    'banner_image.required' => 'Please upload a banner image.',
                    'banner_image.string' => 'Invalid banner image data.',
                ];
            default:
                return [];
        }
    }

    /**
     * Save step data to user profile
     */
    private function saveToProfile($user, $step, $data)
    {
        $profile = $user->profile ?? new Profile();
        $profile->user_id = $user->id;
        $user = User::find(auth()->id());

        switch ($step) {
            case 1:
                $data['lat'];
                $user->name = $data['full_name'] ?? $user->name;
                // $profile->name = $data['full_name'] ?? // $profile->;
                $profile->pic =  $data['avatar'] ?? $profile->pic;
                $profile->company_name = $data['company_name'] ?? $profile->company_name;
                $profile->phone = $data['phone'] ?? $profile->phone;
                $profile->website = $data['website'] ?? $profile->website;
                $profile->facebook = $data['facebook'] ?? $profile->facebook;
                $profile->instagram = $data['instagram'] ?? $profile->instagram;
                $profile->tiktok = $data['tiktok'] ?? $profile->tiktok;
                $profile->location = $data['location'] ?? $profile->location;
                $profile->lat = $data['lat'] ?? $profile->lat;
                $profile->lng = $data['lng'] ?? $profile->lng;
                $profile->location_service = $data['location_service'] ?? $profile->location_service;
                $profile->company_id = $data['company_id'] ?? $profile->company_id;
                $profile->vat_number = $data['vat_number'] ?? $profile->vat_number;
                break;
            case 2:
                $categories = $data['categories'] ?? [];
                $subcategories = $data['subcategories'] ?? [];
                $user->categories()->sync($categories);
                $user->subcategories()->sync($subcategories);
                break;
            case 3:
                $productCertifications = $data['product_certifications'] ?? [];
                $user->product_cerficates()->sync($productCertifications);
                $certifications = $data['certificates'] ?? [];
                UserCertificate::where('user_id', $user->id)->delete();
                foreach ($certifications as $certification) {
                    $userCertificate = new UserCertificate();
                    $userCertificate->user_id = $user->id;
                    $userCertificate->title = $certification['title'] ?? null;
                    $userCertificate->issued_by = $certification['issued_by'] ?? null;
                    $userCertificate->issued_date = $certification['issued_date'] ?? null;
                    $userCertificate->end_date = $certification['end_date'] ?? null;
                    if (isset($certification['image'])) {
                        $userCertificate->image = $this->storeImage('certificates', $certification['image']);
                    } elseif (isset($certification['old_image'])) {
                        $userCertificate->image = $certification['old_image'];
                    }
                    $userCertificate->exception = $certification['exception'] ?? null;
                    $userCertificate->exception_reason = $certification['exception_reason'] ?? null;
                    $userCertificate->save();
                }
                break;
            case 4:
                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                $opening_hours = $data['availability'] ?? [];
                UserOpeningHour::where('user_id', $user->id)->delete();
                foreach ($days as $day) {
                    $hour = collect($opening_hours)->firstWhere('day', $day);

                    $userOpeningHour = new UserOpeningHour();
                    $userOpeningHour->user_id = $user->id;
                    $userOpeningHour->day = $day;

                    if ($hour) {
                        // Convert 12-hour format to 24-hour format for database storage
                        $startTime = isset($hour['start']) ? \Carbon\Carbon::createFromFormat('h:i A', $hour['start'])->format('H:i') : null;
                        $endTime = isset($hour['end']) ? \Carbon\Carbon::createFromFormat('h:i A', $hour['end'])->format('H:i') : null;

                        $userOpeningHour->open = $startTime;
                        $userOpeningHour->close = $endTime;
                        $userOpeningHour->type = "open";
                    } else {
                        $userOpeningHour->open = null;
                        $userOpeningHour->close = null;
                        $userOpeningHour->type = "close";
                    }
                    $userOpeningHour->save();
                }
                // open hour end here

                // Holiday start here

                $holidays = $data['holidays'] ?? [];
                if (!empty($holidays)) {
                    UserHoliday::where('user_id', $user->id)->delete();
                    foreach ($holidays as $holiday) {
                        if (isset($holiday['holiday_id'])) {
                            $exist_holiday = Holiday::where('id', $holiday['holiday_id'])->first();
                            if (!$exist_holiday) {
                                continue;
                            }
                            $userHoliday = new UserHoliday();
                            $userHoliday->user_id = $user->id;
                            $userHoliday->holiday_id = $exist_holiday->id;
                            $userHoliday->name = $holiday['name'] ?? null;
                            $userHoliday->date = $holiday['date'] ?? null;

                            // Convert 12-hour format to 24-hour format for database storage
                            $startTime = isset($holiday['start_time']) && !empty($holiday['start_time'])
                                ? \Carbon\Carbon::createFromFormat('h:i A', $holiday['start_time'])->format('H:i:s')
                                : null;
                            $endTime = isset($holiday['end_time']) && !empty($holiday['end_time'])
                                ? \Carbon\Carbon::createFromFormat('h:i A', $holiday['end_time'])->format('H:i:s')
                                : null;

                            $userHoliday->start_time = $startTime;
                            $userHoliday->end_time = $endTime;
                            if (isset($holiday['start_time'], $holiday['end_time'])) {
                                $userHoliday->is_full_day = 0;
                            } else {
                                $userHoliday->is_full_day = 1;
                            }
                            $userHoliday->is_custom = 0;
                            $userHoliday->save();
                        }
                    }
                }
                $customHolidays = $data['custom_holidays'] ?? [];
                if (!empty($customHolidays)) {
                    UserHoliday::where('user_id', $user->id)->where("is_custom", 1)->delete();

                    foreach ($customHolidays as $index => $value) {
                        // Only process if this is a checkbox value (string) indicating the holiday is checked
                        if (is_string($value)) {
                            // Look for the corresponding data array
                            $customHolidayData = $customHolidays[$index] ?? null;

                            // If we have data for this checked holiday, save it
                            if (is_array($customHolidayData) && isset($customHolidayData['name']) && isset($customHolidayData['date'])) {
                                $rawDate = $customHolidayData['date'];

                                try {
                                    $formattedDate = \Carbon\Carbon::parse($rawDate)->format('Y-m-d');

                                    $exists = UserHoliday::where('user_id', $user->id)
                                        ->where('date', $formattedDate)
                                        ->exists();

                                    if (!$exists) {
                                        $userHoliday = new UserHoliday();
                                        $userHoliday->user_id = $user->id;
                                        $userHoliday->name = $customHolidayData['name'];
                                        $userHoliday->date = $formattedDate;

                                        // Convert 12-hour format to 24-hour format for database storage
                                        $startTime = isset($customHolidayData['start_time']) && !empty($customHolidayData['start_time'])
                                            ? \Carbon\Carbon::createFromFormat('h:i A', $customHolidayData['start_time'])->format('H:i:s')
                                            : null;
                                        $endTime = isset($customHolidayData['end_time']) && !empty($customHolidayData['end_time'])
                                            ? \Carbon\Carbon::createFromFormat('h:i A', $customHolidayData['end_time'])->format('H:i:s')
                                            : null;

                                        $userHoliday->start_time = $startTime;
                                        $userHoliday->end_time = $endTime;
                                        if (isset($customHolidayData['start_time'], $customHolidayData['end_time'])) {
                                            $userHoliday->is_full_day = 0;
                                        } else {
                                            $userHoliday->is_full_day = 1;
                                        }
                                        $userHoliday->is_custom = 1;
                                        $userHoliday->save();
                                    }
                                } catch (\Exception $e) {
                                    continue;
                                }
                            }
                        }
                    }
                }
                // Holiday end here
                break;
            case 5:
                $profile->banner_image = $data['banner_image'];
                $user->registration_completed = 1;
                break;
        }
        $user->save();
        $profile->save();
    }

    /**
     * Handle file uploads for each step
     */
    private function handleFileUploads(Request $request, $step)
    {
        $uploadedFiles = [];
        switch ($step) {
            case 1:
                if ($request->hasFile('avatar')) {
                    $uploadedFiles['avatar'] = $this->storeImage('user-image', $request->file('avatar'));
                }
                break;
        }
        return $uploadedFiles;
    }

    /**
     * Handle cropped image from base64
     */
    private function handleCroppedImage($base64Image)
    {
        if (empty($base64Image)) return null;

        // Decode base64 image
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));
        $filename = 'user-image/' . 'cropped_' . time() . '.png';
        Storage::disk('website')->put($filename, $imageData);
        return $filename;
    }
}
