@extends('Website.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-6 breadcrumbs">
                    <!-- <div class="d-flex justify-content-between align-items-center"> -->
                    <h6 class="sora black">{{ isset($friend) ? 'Edit' : 'Add' }} Your Friend/Family</h6>
                    <p class="fs-14 sora light-black m-0">Friends <span class="mx-3"><i
                                class="fa-solid fa-chevron-right right-arrow"></i></span>
                        {{ isset($friend) ? 'Edit' : 'Add' }} Your Friend/Family </p>
                    <!-- <a href="#!" class="add-btn">Add</a> -->
                    <!-- </div> -->
                </div>
                <div class="col-md-12">
                    @if (isset($friend))
                        <form class="form-add-services" action="{{ route('friends.update', $friend->ids) }}" method="POST"
                            enctype="multipart/form-data">
                        @else
                            <form class="form-add-services" action="{{ route('friends.store') }}" method="POST"
                                enctype="multipart/form-data">
                    @endif
                    @csrf
                    @if (isset($friend))
                        @method('PUT')
                    @endif
                    <div class="row row-gap-8">
                        <div class="col-md-12 d-flex gap-4">
                            <label class="d-flex gap-2 align-items-center">
                                <input class="form-check-input" type="radio" name="role" value="above-13"
                                    {{ old('role', isset($friend) ? $friend->type : 'above-13') == 'above-13' ? 'checked' : '' }}>
                                <span>13+ Age</span>
                            </label>
                            <label class="d-flex gap-2 align-items-center">
                                <input class="form-check-input" type="radio" name="role" value="under-13"
                                    {{ old('role', isset($friend) ? $friend->type : '') == 'under-13' ? 'checked' : '' }}>
                                <span>Under 13</span>
                            </label>
                        </div>
                        <div class="col-md-12">
                            <!-- Divs to show/hide -->
                            <div id="div-13plus"
                                style="display: {{ old('role', isset($friend) ? $friend->type : 'above-13') == 'above-13' ? 'block' : 'none' }};">
                                <div class="row row-gap-5">
                                    <div class="col-md-6">
                                        <label for="service-preferences" class="form-label form-input-labels">Select
                                            User</label>
                                        <select class="form-select form-select-field" id="Users" name="friend_user_id"
                                            data-control="select2" data-placeholder="Select user">
                                            <option></option>
                                            @foreach ($customers as $customer)
                                                <option value="{{ $customer->id }}"
                                                    {{ old('friend_user_id', isset($friend) ? $friend->friend_user_id : '') == $customer->id ? 'selected' : '' }}>
                                                    {{ $customer->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Relation<span class="red">*</span></label>
                                        <input type="text" name="relation" class="form-control form-input"
                                            placeholder="Enter your relation"
                                            value="{{ old('relationship', isset($friend) ? $friend->relationship : '') }}" />
                                    </div>
                                </div>
                            </div>
                            <div id="div-under13"
                                style="display: {{ old('role', isset($friend) ? $friend->type : 'above-13') == 'under-13' ? 'block' : 'none' }};">
                                <div class="row row-gap-5">
                                    <div class="col-md-12">
                                        <label for="description" class="form-label form-input-labels">Thumbnail
                                            Image</label>
                                        <div class="position-relative  form-add-category form-add-services">
                                            <div class="image-input {{ isset($friend) && $friend->profile_pic ? '' : 'image-input-empty' }}"
                                                data-kt-image-input="true">
                                                <div class="image-input-wrapper"
                                                    @if (isset($friend) && $friend->profile_pic) style="background-image: url('{{ asset('website').'/'.$friend->profile_pic }}')" @endif>
                                                </div>
                                                <label
                                                    class="image-label  flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Change avatar">
                                                    <i class="bi bi-upload upload-icon"></i>
                                                    <span>Upload Image</span>
                                                    <span>50x50 px</span>

                                                    <!--begin::Inputs-->
                                                    <input type="file" name="profile_pic" accept=".png, .jpg, .jpeg" />
                                                    <input type="hidden" name="avatar_remove" />
                                                    <!--end::Inputs-->
                                                </label>
                                                <span
                                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Cancel avatar">
                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                </span>
                                                <!--end::Cancel button-->
                                                <!--begin::Remove button-->
                                                <span
                                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                    data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Remove avatar">
                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                </span>
                                                <!--end::Remove button-->
                                            </div>
                                            <!--end::Image input-->
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Name</label>
                                        <input type="text" name="name" class="form-control form-input"
                                            placeholder="Enter your name"
                                            value="{{ old('name', isset($friend) ? $friend->name : '') }}" />
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Relation</label>
                                        <input type="text" name="relationship" class="form-control form-input"
                                            placeholder="Enter your relation"
                                            value="{{ old('relationship', isset($friend) ? $friend->relationship : '') }}" />
                                    </div>
                                    <div class="col-md-6">
                                        <label for="service-preferences" class="form-label form-input-labels">Service
                                            Preferences</label>
                                        <select class="form-select form-select-field" id="service-preferences"
                                            name="service_preferences[]" data-control="select2" multiple
                                            data-placeholder="Select service preferences">
                                            @php
                                                $selectedServices = old(
                                                    'service_preferences',
                                                    isset($friend) && $friend->service_preferences
                                                        ? json_decode($friend->service_preferences, true)
                                                        : [],
                                                );
                                            @endphp
                                            @foreach ($services as $service)
                                                <option value="{{ $service->id }}"
                                                    {{ in_array($service->id, $selectedServices) ? 'selected' : '' }}>
                                                    {{ $service->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="add-btn">{{ isset($friend) ? 'Update' : 'Add' }}</button>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        // Wait for jQuery to be available
        function initFriendsForm() {
            if (typeof $ === 'undefined') {
                setTimeout(initFriendsForm, 100);
                return;
            }

            $(document).ready(function() {
                // Only run if we're on the friends form page
                if ($('input[name="role"]').length === 0) {
                    return;
                }

                console.log('Document ready - Friends form loaded');

                // Function to toggle divs based on radio selection
                function toggleDivs() {
                    const radioButtons = $('input[name="role"]:checked');
                    if (radioButtons.length === 0) return;

                    const selectedRole = radioButtons.val();
                    console.log('Selected role:', selectedRole);

                    const div13plus = $('#div-13plus');
                    const divUnder13 = $('#div-under13');

                    if (div13plus.length === 0 || divUnder13.length === 0) return;

                    if (selectedRole === 'above-13') {
                        console.log('Showing above-13 div, hiding under-13 div');
                        div13plus.show();
                        divUnder13.hide();
                    } else if (selectedRole === 'under-13') {
                        console.log('Showing under-13 div, hiding above-13 div');
                        div13plus.hide();
                        divUnder13.show();
                    }
                }

                // Test if elements exist
                console.log('Radio buttons found:', $('input[name="role"]').length);
                console.log('Above-13 div found:', $('#div-13plus').length);
                console.log('Under-13 div found:', $('#div-under13').length);

                // Handle radio button change only if elements exist
                const roleRadios = $('input[name="role"]');
                if (roleRadios.length > 0) {
                    roleRadios.on('change', function() {
                        console.log('Radio button changed');
                        toggleDivs();
                    });

                    // Also handle click event as backup
                    roleRadios.on('click', function() {
                        console.log('Radio button clicked');
                        toggleDivs();
                    });
                }
            });
        }

        // Initialize when page loads
        initFriendsForm();
    </script>
@endpush
