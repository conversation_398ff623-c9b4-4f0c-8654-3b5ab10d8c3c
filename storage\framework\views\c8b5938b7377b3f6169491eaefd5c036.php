<?php $__env->startPush('css'); ?>
<link rel="stylesheet" href="<?php echo e(asset('website/assets/css/chat.css')); ?>">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/emoji-picker-element@^1/index.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div id="kt_app_content" class="app-content flex-column-fluid <?php echo e(auth()->user()->hasRole('customer') ? 'customer_dashboard bg-color' : ''); ?>">
    <div id="kt_app_content_container" class="container-fluid <?php echo e(auth()->user()->hasRole('customer') ? '' : ''); ?>">
        <div class="row g-0">
            <div class="col-12">
                <div class="chat-container d-flex">
                    <!-- Chat Sidebar -->
                    <div class="chat-sidebar">
                        <div class="chat-sidebar-header">
                            <div class="chat-dropdown">
                                <button class="chat-dropdown-toggle" type="button">
                                    Chats
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="chat-search">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="chat-search-input" placeholder="Search conversations..." autocomplete="off">
                            </div>
                        </div>
                        <div id="conversations-list">
                            <div class="text-center p-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Main Area -->
                    <div class="chat-main">
                        <div id="chat-area">
                            <div class="empty-state">
                                <i class="fas fa-comments"></i>
                                <h5>Select a conversation to start messaging</h5>
                                <p class="text-muted">Choose from your existing conversations or start a new one</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Upload Modal -->
<div class="modal fade" id="fileUploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Select files to send</label>
                    <input type="file" class="form-control" id="fileInput" multiple accept="image/*,video/*,.pdf,.doc,.docx,.txt">
                    <div class="form-text">
                        Max: 10 images, 3 videos, 5 documents. Max size: 50MB per file.
                    </div>
                </div>
                <div id="filePreview" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="sendFilesBtn">Send Files</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script src="<?php echo e(asset('website/assets/js/chat.js')); ?>?v=<?php echo e(time()); ?>"></script>
<script>
    // Pusher is now initialized globally in master layout
    // No need to initialize it again here

    // Set current user ID for JavaScript access
    window.currentUserId = '<?php echo e(auth()->id()); ?>';

    // Initialize chat when page loads
    $(document).ready(function() {
        // Set current user ID for message alignment
        ChatApp.currentUserId = <?php echo json_encode(auth()->id(), 15, 512) ?>;

        // Get conversation ID from URL parameter or backend
        const urlParams = new URLSearchParams(window.location.search);
        const urlConversationId = urlParams.get('conversation_id'); // This might be IDS
        const backendConversationId = <?php echo json_encode($conversationId ?? null, 15, 512) ?>; // This is database ID

        // Use backend conversation ID (already converted from IDS to database ID)
        const conversationId = backendConversationId || urlConversationId;

        // Check if ChatApp is defined
        if (typeof ChatApp !== 'undefined') {
            ChatApp.init(conversationId);
        } else {
            console.error('ChatApp is not defined. Please check if chat.js is loaded properly.');
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/chats/index.blade.php ENDPATH**/ ?>