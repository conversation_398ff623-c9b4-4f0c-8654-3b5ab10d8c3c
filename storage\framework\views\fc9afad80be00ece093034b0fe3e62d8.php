<div class="modal fade card-details" id="edit-user-galleries-modal" aria-labelledby="edit-user-galleries-modal"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Edit User Galleries</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-user-galleries-form" action="<?php echo e(route('dashboard.galleries.save')); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <!-- Gallery Images Section -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="sora black fs-16 semi_bold mb-0">Gallery Images</h6>
                                <button type="button" class="add-btn btn-sm add-gallery-btn">
                                    <i class="ki-outline ki-plus fs-6"></i> Add More
                                </button>
                            </div>
                            <div id="gallery-blocks-container">
                                <?php $index = 0; ?>
                                <?php $__empty_1 = true; $__currentLoopData = $user->galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <!-- Existing gallery block -->
                                    <div class="gallery-block mb-4" data-index="<?php echo e($index); ?>">
                                        <div class="row align-items-center">
                                            <div class="col-md-10">
                                                <label for="gallery-image-<?php echo e($index); ?>" class="form-label form-input-labels">Image <?php echo e($index + 1); ?></label>
                                                <div class="position-relative form-add-services">
                                                    <div class="image-input image-input-changed image-input-circle" data-kt-image-input="true"
                                                        style="background-image: url('<?php echo e(asset('website') . '/' . $gallery->image); ?>')">
                                                        <div class="image-input-wrapper w-125px h-125px"
                                                            style="background-image: url('<?php echo e(asset('website') . '/' . $gallery->image); ?>')">
                                                        </div>
                                                        <label
                                                            class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Change image">
                                                            <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                                    class="path2"></span></i>
                                                            <input type="file" name="galleries[<?php echo e($index); ?>][image]" accept=".png, .jpg, .jpeg" />
                                                            <input type="hidden" name="galleries[<?php echo e($index); ?>][old_image]" value="<?php echo e($gallery->image); ?>" />
                                                        </label>
                                                        <span
                                                            class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Cancel image">
                                                            <i class="ki-outline ki-cross fs-3"></i>
                                                        </span>
                                                        <span
                                                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Remove image">
                                                            <i class="ki-outline ki-cross fs-3"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 d-flex flex-end">
                                                <button type="button" class="btn btn-danger btn-sm delete-gallery-block-btn <?php echo e($user->galleries->count() <= 1 ? 'd-none' : ''); ?>">
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <?php $index++; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <!-- Initial empty gallery block when no existing images -->
                                    <div class="gallery-block mb-4" data-index="0">
                                        <div class="row align-items-center">
                                            <div class="col-md-10">
                                                <label for="gallery-image-0" class="form-label form-input-labels">Image 1</label>
                                                <div class="position-relative form-add-services">
                                                    <div class="image-input image-input-empty image-input-circle" data-kt-image-input="true"
                                                        style="background-image: url('/website/assets/images/image_input_holder.png')">
                                                        <div class="image-input-wrapper w-125px h-125px">
                                                        </div>
                                                        <label
                                                            class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Change image">
                                                            <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                                    class="path2"></span></i>
                                                            <input type="file" name="galleries[0][image]" accept=".png, .jpg, .jpeg" />
                                                        </label>
                                                        <span
                                                            class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Cancel image">
                                                            <i class="ki-outline ki-cross fs-3"></i>
                                                        </span>
                                                        <span
                                                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Remove image">
                                                            <i class="ki-outline ki-cross fs-3"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 d-flex flex-end">
                                                <button type="button" class="btn btn-danger btn-sm delete-gallery-block-btn d-none">
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/profile_settings/modal/edit-user-galleries-modal.blade.php ENDPATH**/ ?>