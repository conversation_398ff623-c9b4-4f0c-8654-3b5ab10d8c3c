@extends('Website.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container family padding-block">
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="sora black">Friends</h4>
                        <a href="{{ route('friends.create') }}" class="add-btn"><i class="fa-solid fa-plus me-3"></i>Add a
                            Friend/Family</a>
                    </div>
                </div>

                <div class="col-md-12 family-tabs">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active service-tab" id="my-friends-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-my-friends" type="button" role="tab"
                                aria-controls="pills-my-friends" aria-selected="true">My Friends
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link service-tab" id="friend-requests-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-friend-requests" type="button" role="tab"
                                aria-controls="pills-friend-requests" aria-selected="true">Friend
                                Requests
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-my-friends" role="tabpanel"
                            aria-labelledby="my-friends-tab" tabindex="0">
                            <div class="blue-box">
                                <div class="row row-gap-5">
                                    <div class="col-md-12 d-flex gap-4">
                                        @include('svg.family')
                                        <div>
                                            <h4 class="sora black">Your Family Members/Friends</h4>
                                            <p class="light-black sora fs-14">Lorem ipsum dolor sit amet
                                                consectetur. Duis arcu sapien justo nulla.</p>
                                        </div>
                                    </div>
                                    @forelse ($friends as $friend)
                                        <div class="col-md-3">
                                            <div class="card friends-cards bg-white h-100">
                                                <div class="card-header p-4 align-items-center">
                                                    <div class="d-flex justify-content-start gap-3 align-items-center">
                                                        @if ($friend->type == 'under-13')
                                                            {{-- Under-13 friends have profile pictures --}}
                                                            <img src="{{ asset('website') . '/' . $friend->profile_pic }}"
                                                                class="h-60px w-60px object-fit-cover rounded-pill top-rated-image"
                                                                alt="{{ $friend->name }}">
                                                        @else
                                                            {{-- Above-13 friends use their user profile or default --}}
                                                            <img src="{{ asset('website' . '/' . $friend->friendUser->profile->pic ?? 'assets/images/family1.png') }}"
                                                                class="h-60px w-60px object-fit-cover rounded-pill top-rated-image"
                                                                alt="{{ $friend->name }}">
                                                        @endif
                                                        <div>
                                                            <p class="sora black fs-16 m-0 semi_bold">{{ $friend->name }}
                                                            </p>
                                                            <p class="light-black fs-14 opacity-6 m-0">
                                                                {{ $friend->relationship }}</p>
                                                            <span
                                                                class="badge badge-sm {{ $friend->type == 'under-13' ? 'badge-primary' : 'badge-success' }}">
                                                                {{ $friend->type == 'under-13' ? 'Under 13' : '13+ User' }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <form action="{{ route('friends.destroy', $friend->ids) }}"
                                                        method="POST" class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="button"
                                                            class="drop-btn delete-btn btn btn-outline-danger py-2 px-3 text-center"
                                                            onclick="showDeleteConfirmation(this)" aria-label="Delete">
                                                            <i class="bi bi-trash p-0 red" aria-hidden="true"></i>
                                                            <span class="visually-hidden">Delete</span>
                                                        </button>
                                                    </form>
                                                </div>
                                                <div class="card-body p-4">
                                                    @if ($friend->type == 'above-13' && $friend->friendUser)
                                                        {{-- Above-13 friends show user details --}}
                                                        <div class="d-flex flex-column gap-3">
                                                            <span class="fs-12 normal sora light-black">
                                                                <span class="me-3">@include('svg.building')</span>
                                                                {{ $friend->friendUser->email }}
                                                            </span>
                                                            @if ($friend->friendUser->phone)
                                                                <span class="fs-12 normal sora light-black">
                                                                    <i class="fa-solid fa-phone me-3"></i>
                                                                    {{ $friend->friendUser->phone }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    @else
                                                        {{-- Under-13 friends show service preferences --}}
                                                        @if ($friend->service_preferences)
                                                            @php
                                                                $services = json_decode(
                                                                    $friend->service_preferences,
                                                                    true,
                                                                );
                                                            @endphp
                                                            <div class="d-flex flex-column gap-2">
                                                                <small class="text-muted">Service Preferences:</small>
                                                                <div class="d-flex flex-wrap gap-1">
                                                                    @foreach ($services as $serviceId)
                                                                        @php
                                                                            $service = \App\Models\Service::find(
                                                                                $serviceId,
                                                                            );
                                                                        @endphp
                                                                        @if ($service)
                                                                            <span
                                                                                class="badge badge-light">{{ $service->name }}</span>
                                                                        @endif
                                                                    @endforeach
                                                                </div>
                                                            </div>
                                                        @endif
                                                    @endif

                                                    @if ($friend->type == 'above-13' && $friend->friendUser->socials)
                                                        <div class="d-flex gap-4 mt-5">
                                                            @foreach ($friend->friendUser->socials as $social)
                                                                <a href="{{ $social->link }}" target="_blank"
                                                                    class="logo-box">
                                                                    <img src="{{ asset('website') . '/' . $social->image }}"
                                                                        alt="social-logo" height="30px" width="30px">
                                                                </a>
                                                            @endforeach
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="card-footer d-flex justify-content-between p-4">
                                                    <a href="{{ route('friends.show', $friend->ids) }}"
                                                        class="button1 px-xxl-17">View Profile</a>
                                                    <a href="{{ route('friends.edit', $friend->ids) }}"
                                                        class="gray-btn">Edit</a>
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="col-md-12 text-center py-5">
                                            <div class="text-muted">
                                                <i class="fa-solid fa-users fa-3x mb-3"></i>
                                                <h5>No Friends Yet</h5>
                                                <p>Start by adding your first friend or family member!</p>
                                            </div>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-friend-requests" role="tabpanel"
                            aria-labelledby="friend-requests-tab" tabindex="0">
                            <div class="blue-box">
                                <div class="row row-gap-5">
                                    <div class="col-md-12 d-flex gap-4">
                                        @include('svg.family')
                                        <div>
                                            <h4 class="sora black">Friend Request: Accept/Reject</h4>
                                            <p class="light-black sora fs-14">Lorem ipsum dolor sit amet
                                                consectetur. Duis arcu sapien justo nulla.</p>
                                        </div>
                                    </div>
                                    @forelse ($pending_friends as $pending_friend)
                                        <div class="col-md-3">
                                            <div class="card friends-cards bg-white">
                                                <div
                                                    class="card-header justify-content-start gap-3 p-4 align-items-center">
                                                    {{-- Pending friends are requests sent TO me by other users --}}
                                                    <img src="{{ asset('website') . '/' . $pending_friend->user->profile->pic ?? 'assets/images/family1.png' }}"
                                                        class="h-60px w-60px object-fit-cover rounded-pill top-rated-image"
                                                        alt="{{ $pending_friend->user ? $pending_friend->user->name : 'User' }}">
                                                    <div>
                                                        <p class="sora black fs-16 m-0 semi_bold">
                                                            {{ $pending_friend->user ? $pending_friend->user->name : 'Unknown User' }}
                                                        </p>
                                                        <p class="light-black fs-14 opacity-6 m-0">
                                                            Wants to be your {{ $pending_friend->relationship }}</p>
                                                        <span class="badge badge-warning">Pending Request</span>
                                                        <span class="badge badge-sm badge-success">13+ User</span>
                                                    </div>
                                                </div>
                                                <div class="card-body p-4">
                                                    {{-- Show details of the user who sent the request --}}
                                                    @if ($pending_friend->user)
                                                        <div class="d-flex flex-column gap-3">
                                                            <span class="fs-12 normal sora light-black">
                                                                <span class="me-3">@include('svg.building')</span>
                                                                {{ $pending_friend->user->email }}
                                                            </span>
                                                            @if ($pending_friend->user->phone)
                                                                <span class="fs-12 normal sora light-black">
                                                                    <i class="fa-solid fa-phone me-3"></i>
                                                                    {{ $pending_friend->user->phone }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="card-footer d-flex justify-content-between p-4">
                                                    <form action="{{ route('friends.accept', $pending_friend->ids) }}"
                                                        method="POST" class="d-inline">
                                                        @csrf
                                                        @method('PATCH')
                                                        <button type="submit" class="button1 px-xxl-17">Accept</button>
                                                    </form>
                                                    <form action="{{ route('friends.reject', $pending_friend->ids) }}"
                                                        method="POST" class="d-inline">
                                                        @csrf
                                                        @method('PATCH')
                                                        <button type="submit" class="gray-btn"
                                                            onclick="return confirm('Are you sure you want to reject this friend request?')">Reject</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="col-md-12 text-center py-5">
                                            <div class="text-muted">
                                                <i class="fa-solid fa-clock fa-3x mb-3"></i>
                                                <h5>No Pending Requests</h5>
                                                <p>You don't have any pending friend requests at the moment.</p>
                                            </div>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
