<!DOCTYPE html>
<html lang="en">

<head>
    <base href="" />
    <title><?php echo e(setting()->title ?? ''); ?></title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="<?php echo e(setting()->title ?? ''); ?>" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="<?php echo e(setting()->title ?? ''); ?>" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('website') . '/' . setting()->favicon ?? ''); ?>" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/rangeslider.js/2.3.3/rangeslider.css">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <!-- Dashboard CSS for professional profile and other dashboard-specific styling -->
    <link href="<?php echo e(asset('website')); ?>/assets/css/dashboard.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/dashboard-responsive.css" rel="stylesheet" type="text/css" />
    <?php echo $__env->yieldPushContent('css'); ?>
</head>

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu"
    class="bg-body position-relative app-blank <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?> customer_dashboard <?php endif; ?>">
    <div id="secLoader">
        <div class="logo-loader">
            <div class="logo-container">
                <!-- <div class="circle">
                    <img src="<?php echo e(asset('website') . '/' . setting()->loader_logo); ?>" class="h-100 w-100 object-fit-contain"
                        alt="logo">
                </div> -->
                <img src="<?php echo e(asset('website')); ?>/assets/images/header_primary.svg" class="h-50 w-50 object-fit-contain"
                    alt="logo">
            </div>
        </div>
    </div>
    <div class="d-flex flex-column flex-root" id="kt_app_root">

        <?php echo $__env->make('website.template.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->yieldContent('content'); ?>
        <?php echo $__env->make('website.template.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </div>
    <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="fa-solid fa-chevron-up"></i>
    </div>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>

    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    
    <!-- jQuery (required) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Swiper JS -->

    <!-- sweetalert JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

    <?php echo $__env->yieldPushContent('js'); ?>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        function swiperScript() {
            if ($(".service-swipper").length) {
                var topRatedSwiper = new Swiper(".service-swipper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 30,
                    navigation: {
                        nextEl: ".top-rated-next",
                        prevEl: ".top-rated-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true,
                    },
                    breakpoints: {
                        320: {
                            slidesPerView: 1,
                            spaceBetween: 20
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 30
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 30
                        }
                    }
                });
            }
            if ($(".mySwiper2").length) {
                var mySwiper2 = new Swiper(".mySwiper2", {
                    slidesPerView: 4,
                    spaceBetween: 20,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }
            if ($(".review-swiper").length) {
                var reviewSwiper = new Swiper(".review-swiper", {
                    slidesPerView: 4,
                    spaceBetween: 15,
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }

            if ($(".cert-swiper").length) {
                var certSwiper = new Swiper(".cert-swiper", {
                    slidesPerView: 4,
                    spaceBetween: 10,
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }

            if ($(".meet-the-team-swiper").length) {
                var teamSwiper = new Swiper(".meet-the-team-swiper", {
                    slidesPerView: 4,
                    spaceBetween: 15,
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }
        }

        // Initialize swipers when document is ready
        $(document).ready(function () {
            swiperScript();
        });
    </script>
    <script type="text/javascript">
        <?php if(session()->has('message')): ?>
            Swal.fire({
                title: "<?php echo e(session()->get('title') ?? 'success!'); ?>",
                html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 600000,
                buttons: false,
            });
        <?php endif; ?>
        <?php if(session()->has('flash_message')): ?>
            Swal.fire({
                title: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 600000,
                buttons: false,
            });
        <?php endif; ?>
        //delete button confirm swal dynamic.
        function showDeleteConfirmation(button) {
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to delete this item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    button.closest('.delete-form').submit();
                }
            });
        }

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>
    <script>

        // select2
        $(document).ready(function () {

            $('.select2-filter').select2({
                minimumResultsForSearch: Infinity,
                width: 'auto',
                dropdownAutoWidth: true
            });
        });

        $(document).ready(function () {
            const navLinks = $('.table-content');
            const sections = $('.content > div[id]');

            // Scroll on click
            navLinks.on('click', function (e) {
                e.preventDefault();
                const targetId = $(this).attr('href');
                const offsetTop = $(targetId).offset().top - 20;

                $('html, body').stop().animate({
                    scrollTop: offsetTop
                }, 400);

                navLinks.removeClass('active');
                $(this).addClass('active');
            });

            // Scroll Spy
            $(window).on('scroll', function () {
                const scrollMiddle = $(window).scrollTop() + $(window).height() / 2;
                let currentId = '';

                sections.each(function () {
                    const sectionTop = $(this).offset().top;
                    const sectionBottom = sectionTop + $(this).outerHeight();

                    if (scrollMiddle >= sectionTop && scrollMiddle <= sectionBottom) {
                        currentId = $(this).attr('id');
                        return false; // break loop
                    }
                });

                if (currentId) {
                    navLinks.removeClass('active');
                    $('.table-content[href="#' + currentId + '"]').addClass('active');
                }
            });


            $(window).trigger('scroll');
        });
        // loader
        var loader = document.getElementById("secLoader");
        window.addEventListener("load", function () {
            loader.style.display = "none"
        });

        flatpickr("#datePicker", {
            dateFormat: "Y-m-d",
            minDate: "today"
        });

        flatpickr("#timePicker", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "h:i K",
            time_24hr: false
        });

        $(document).ready(function () {
            // Assign data-sub-category attributes to category boxes
            $('.service-drop-down #tab-personal-trainers .category-box').each(function (index) {
                const dataAttr = 'services-personal-trainer-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            $('.service-drop-down #tab-makeup-artists .category-box').each(function (index) {
                const dataAttr = 'services-makeup-artist-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            $('.professional-drop-down #tab-personal-trainers .category-box').each(function (index) {
                const dataAttr = 'professional-personal-trainer-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            // Redirect on category-box click (only if not already on target URL)
            $(document).on('click', '.category-box', function () {
                const $this = $(this);
                const subcategorySlug = $this.data('sub-category');
                const categoryPane = $this.closest('.tab-pane').attr('id');
                let targetUrl = '';

                if ($this.closest('.service-drop-down').length) {
                    targetUrl = '/services?category-name=' + categoryPane + '&subcategory-name=' +
                        subcategorySlug;
                } else if ($this.closest('.professional-drop-down').length) {
                    targetUrl = '/professional?category-name=' + categoryPane + '&subcategory-name=' +
                        subcategorySlug;
                }

                const fullTargetUrl = window.location.origin + targetUrl;

                if (targetUrl && window.location.href !== fullTargetUrl) {
                    window.location.href = targetUrl;
                }
            });

            // On page load: activate tab and scroll or click subcategory
            const params = new URLSearchParams(window.location.search);
            const categoryName = params.get('category-name');
            const subcategoryName = params.get('subcategory-name');

            if (categoryName) {
                const $mainTabBtn = $('[data-bs-target="#' + categoryName + '"]');
                const $subcategoryBox = $('[data-sub-category="' + subcategoryName + '"]');

                if ($mainTabBtn.length) {
                    $mainTabBtn.trigger('click');

                    if ($subcategoryBox.length) {
                        setTimeout(() => {
                            if ($subcategoryBox.hasClass('nav-link')) {
                                $subcategoryBox.trigger('click');
                            } else {
                                $subcategoryBox[0].scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }

                            $subcategoryBox.addClass('active');
                            setTimeout(() => $subcategoryBox.removeClass('highlight'), 2000);
                        }, 300);
                    }
                }
            }
        });

        // DataTables initialization
        $(document).ready(function () {
            if ($(".responsiveTable").length) {
                var table = $(".responsiveTable").DataTable({
                    dom: "rtip",
                    responsive: true,
                    scrollX: false,
                    paging: true,
                    pageLength: 8,
                    lengthChange: false,
                    initComplete: function () {
                        // Set data-label attributes from header text
                        this.api()
                            .columns()
                            .header()
                            .each(function (header) {
                                var title = $(header).text();
                                $(header).attr("data-label", title);
                            });
                    },
                });

                // Update data-labels when table changes
                table.on("draw", function () {
                    $("th").each(function () {
                        var title = $(this).text();
                        $(this).attr("data-label", title);
                    });
                });

                $("#customSearchInput").on("keyup", function () {
                    table.search(this.value).draw();
                });
            }
        });

        // favourite functionality
        $(document).ready(function () {
            $('.fav-icon i').on('click', function () {
                const $icon = $(this);

                // Toggle class
                $icon.toggleClass('fa-solid fa-regular text-danger');

                // Show toast only if it's being added
                if ($icon.hasClass('fa-solid')) {
                    $('#toast').stop(true, true).fadeIn(300).delay(1500).fadeOut(400);
                }
            });
        });

    </script>

    <!-- Pusher for Global Delivery Notifications -->
    <?php if(auth()->guard()->check()): ?>
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <script>
        $(document).ready(function() {
            // Check if Pusher is loaded
            if (typeof Pusher === 'undefined') {
                return;
            }

            // Check environment variables
            var pusherKey = '<?php echo e(env('PUSHER_APP_KEY')); ?>';
            var pusherCluster = '<?php echo e(env('PUSHER_APP_CLUSTER')); ?>';

            if (!pusherKey || !pusherCluster) {
                return;
            }

            try {
                // Initialize Pusher
                window.Pusher = Pusher;
                window.pusher = new Pusher(pusherKey, {
                    cluster: pusherCluster,
                    encrypted: true,
                    auth: {
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    },
                    authEndpoint: '/broadcasting/auth'
                });

                // Set up delivery status handler
                var currentUserId = '<?php echo e(auth()->id()); ?>';

                if (!currentUserId) {
                    return;
                }

                // Subscribe to user-specific channel for delivery notifications
                var globalUserChannel = window.pusher.subscribe('user.' + currentUserId);

                globalUserChannel.bind('message.received', function(data) {
                    // Only handle delivery status for messages not from current user
                    if (data.message.sender.id != currentUserId) {
                        // Mark as delivered via AJAX
                        $.ajax({
                            url: '/chats/messages/' + data.message.id + '/delivered',
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            }
                        });

                        // Check if this conversation is currently open (only on chat page)
                        if (window.location.pathname.includes('/chats')) {
                            var urlParams = new URLSearchParams(window.location.search);
                            var urlConversationId = urlParams.get('conversation_id');
                            var isConversationOpen = (urlConversationId === data.conversation_ids);

                            if (isConversationOpen) {
                                $.ajax({
                                    url: '/chats/mark-read/' + data.message.conversation_id,
                                    method: 'POST',
                                    data: {
                                        _token: $('meta[name="csrf-token"]').attr('content')
                                    }
                                });
                            }
                        }

                        // Update envelope counter directly
                        if (typeof ChatApp !== 'undefined' && ChatApp.updateEnvelopeCounterDirect) {
                            ChatApp.updateEnvelopeCounterDirect();
                        } else {
                            // Fallback for non-chat pages
                            updateEnvelopeCounter();
                        }

                        // Update sidebar if on chat page
                        if (window.location.pathname.includes('/chats') && typeof ChatApp !== 'undefined') {
                            ChatApp.loadConversations();
                        }
                    }
                });
            } catch (error) {
                // Silent error handling
            }
        });

        // Envelope counter functions
        function updateEnvelopeCounter() {
            $.ajax({
                url: '/chats/unread-count',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        const count = response.count;
                        const counter = $('#envelope-counter');

                        if (count > 0) {
                            counter.text(count > 99 ? '99+' : count).show();
                        } else {
                            counter.hide();
                        }
                    }
                }
            });
        }

        // Initialize counter on page load
        $(document).ready(function() {
            <?php if(auth()->guard()->check()): ?>
            updateEnvelopeCounter();
            <?php endif; ?>
        });
    </script>
    <?php endif; ?>

</body>

</html><?php /**PATH D:\SALMAN\git\anders\resources\views/Website/layout/master.blade.php ENDPATH**/ ?>