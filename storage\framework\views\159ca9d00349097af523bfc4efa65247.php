<script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA1Qkj7ocEmNs4U5-T2vVJ04OrsCRH0tCU&libraries=places&callback=initMap&v=weekly"
    async defer></script>
<script>
    let autocomplete;
    function initMap() {
        const input = document.getElementById("pac-input");
        autocomplete = new google.maps.places.Autocomplete(input);

        autocomplete.addListener("place_changed", () => {
            const place = autocomplete.getPlace();
            if (!place.geometry || !place.geometry.location) {
                console.log("No details available for input: '" + place.name + "'");
                return;
            }
            document.getElementById("latitude").value = place.geometry.location.lat();
            document.getElementById("longitude").value = place.geometry.location.lng();
        });
        const latFromRequest = "<?php echo e(request('lat')); ?>";
        const lngFromRequest = "<?php echo e(request('lng')); ?>";

        if (latFromRequest && lngFromRequest) {
            const pos = {
                lat: parseFloat(latFromRequest),
                lng: parseFloat(lngFromRequest)
            };

            // Update inputs
            document.getElementById("latitude").value = pos.lat;
            document.getElementById("longitude").value = pos.lng;

            // Reverse geocode to get address and populate input
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                location: pos
            }, (results, status) => {
                if (status === "OK" && results[0]) {
                    document.getElementById("pac-input").value = results[0].formatted_address;
                }
            });

        } else if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const pos = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    document.getElementById("latitude").value = pos.lat;
                    document.getElementById("longitude").value = pos.lng;

                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: pos
                    }, (results, status) => {
                        if (status === "OK" && results[0]) {
                            document.getElementById("pac-input").value = results[0].formatted_address;
                        }
                    });
                },
                (error) => {
                    console.log("Geolocation error:", error);
                }
            );
        }
    }
</script>
<?php /**PATH D:\SALMAN\git\anders\resources\views/layouts/includes/current-location.blade.php ENDPATH**/ ?>