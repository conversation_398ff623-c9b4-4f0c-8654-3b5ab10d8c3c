@extends('website.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-6 breadcrumbs">
                    <!-- <div class="d-flex justify-content-between align-items-center"> -->
                    <h6 class="sora black">Add Your Friend/Family</h6>
                      <p class="fs-14 sora light-black m-0">Friends <span class="mx-3"><i
                                class="fa-solid fa-chevron-right right-arrow"></i></span> Add Your Friend/Family </p>
                    <!-- <a href="#!" class="add-btn">Add</a> -->
                    <!-- </div> -->
                </div>
                <div class="col-md-12">
                    <form class="form-add-services ">
                        <div class="row row-gap-8">
                            <div class="col-md-12 d-flex gap-4">
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="role" value="13+ Age" checked>
                                    <span>13+ Age</span>
                                </label>
                                <label class="d-flex gap-2 align-items-center">
                                    <input class="form-check-input" type="radio" name="role" value="Under 13">
                                    <span>Under 13</span>
                                </label>
                            </div>
                            <div class="col-md-12">
                                <!-- Divs to show/hide -->
                                <div class="form-hide-box" id="div-13plus">
                                    <div class="row row-gap-5">
                                        <div class="col-md-6">
                                            <label for="email-address" class="form-label">Email Address <span class="red">*</span></label>
                                            <input type="text" name="email-address" id="email-address"
                                                class="form-control form-input" placeholder="Enter your email" required />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Relation<span class="red">*</span></label>
                                            <input type="text" name="fullname" class="form-control form-input"
                                                placeholder="Enter your relation" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-hide-box" id="div-under13">
                                    <div class="row row-gap-5">

                                        <div class="col-md-12">

                                            <label for="description" class="form-label form-input-labels">Thumbnail
                                                Image</label>
                                            <div class="position-relative  form-add-category form-add-services">
                                                <div class="image-input image-input-empty" data-kt-image-input="true">
                                                    <div class="image-input-wrapper"></div>
                                                    <label
                                                        class="image-label  flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Change avatar">
                                                        <i class="bi bi-upload upload-icon"></i>
                                                        <span>Upload Image</span>
                                                        <span>50x50 px</span>

                                                        <!--begin::Inputs-->
                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                                        <input type="hidden" name="avatar_remove" />
                                                        <!--end::Inputs-->
                                                    </label>

                                                    <span
                                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Cancel avatar">
                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                    </span>
                                                    <!--end::Cancel button-->

                                                    <!--begin::Remove button-->
                                                    <span
                                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Remove avatar">
                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                    </span>
                                                    <!--end::Remove button-->
                                                </div>
                                                <!--end::Image input-->
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Name</label>
                                            <input type="text" name="fullname" class="form-control form-input"
                                                placeholder="Enter your name" />
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Relation</label>
                                            <input type="text" name="fullname" class="form-control form-input"
                                                placeholder="Enter your relation" />
                                        </div>
                                        <div class="col-md-6">
                                            <label for="service-preferences" class="form-label form-input-labels">Service
                                                Preferences</label>
                                            <select class="form-select form-select-field" id="service-preferences"
                                                name="service-preferences" data-control="select2"
                                                data-placeholder="Select service preferences">
                                                <option></option>
                                                <option value="Trainer">Trainer</option>
                                                <option value="Yoga">Yoga</option>
                                            </select>
                                        </div>
                                        <!-- <div class="col-md-6">
                                                <label class="form-label">Location</label>
                                                <input type="text" name="Location" class="form-control form-input"
                                                    placeholder="Enter your location" />
                                            </div> -->
                                    </div>
                                </div>


                            </div>

                            <div class="col-md-6">
                                <a href="#!" class="add-btn">Add</a>
                            </div>

                        </div>
                    </form>
                </div>

            </div>

        </div>
    </div>
@endsection
