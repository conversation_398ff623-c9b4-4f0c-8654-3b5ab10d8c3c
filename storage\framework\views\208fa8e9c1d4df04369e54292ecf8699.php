<!-- Body Specifications Modal -->
<div class="modal fade card-details" id="edit-social-modal" aria-labelledby="personal-info-modal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Edit Socials</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-social-form" action="<?php echo e(route('socials.store')); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3 d-flex flex-end">
                        <button type="button" class="add-btn add-social-btn">Add More</button>
                    </div>
                    <div id="social-blocks-container">
                        <?php if(auth()->user()->socials && auth()->user()->socials->count() > 0): ?>
                            <?php $__currentLoopData = auth()->user()->socials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="social-block mb-4" data-index="<?php echo e($index); ?>">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5 class="mb-0">Social <?php echo e($index + 1); ?></h5>
                                        <?php if(!($loop->first)): ?>
                                            <button type="button" class="btn btn-danger btn-sm delete-social-btn">Delete
                                                Block
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                    <div class="row row-gap-3">
                                        <div class="col-md-12">
                                            <label for="social-image-<?php echo e($index); ?>" class="form-label form-input-labels">Social
                                                Logo</label>
                                            <div class="position-relative form-add-services">
                                                <div class="image-input image-input-empty image-input-circle"
                                                    data-kt-image-input="true"
                                                    style="background-image: url(<?php echo e(asset('website') . '/' . $social->image ?? '/website/assets/images/image_input_holder.png'); ?>)">
                                                    <div class="image-input-wrapper w-125px h-125px">
                                                    </div>
                                                    <label
                                                        class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Change avatar">
                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                                class="path2"></span></i>
                                                        <input type="file" name="socials[<?php echo e($index); ?>][image]"
                                                            accept=".png, .jpg, .jpeg" />
                                                        <input type="hidden" name="socials[<?php echo e($index); ?>][image_remove]" />
                                                    </label>
                                                    <span
                                                        class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Cancel avatar">
                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                    </span>
                                                    <span
                                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Remove avatar">
                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="social-name-<?php echo e($index); ?>" class="form-label form-input-labels">Social
                                                Name</label>
                                            <input type="text" class="form-control form-inputs-field"
                                                placeholder="Enter social name" id="social-name-<?php echo e($index); ?>"
                                                name="socials[<?php echo e($index); ?>][name]" value="<?php echo e($social->name ?? ''); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="social-link-<?php echo e($index); ?>" class="form-label form-input-labels">Social
                                                Link</label>
                                            <input type="text" class="form-control form-inputs-field"
                                                placeholder="Enter social link" id="social-link-<?php echo e($index); ?>"
                                                name="socials[<?php echo e($index); ?>][link]" value="<?php echo e($social->link ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="gray-card  social-block mb-4" data-index="0">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0">Social 1</h5>
                                </div>
                                <div class="row row-gap-3">
                                    <div class="col-md-12">
                                        <label for="social-image-0" class="form-label form-input-labels">Social
                                            Logo</label>
                                        <div class="position-relative form-add-services">
                                            <div class="image-input image-input-empty image-input-circle"
                                                data-kt-image-input="true"
                                               >
                                                <div class="image-input-wrapper w-125px h-125px">
                                                </div>
                                                <label
                                                    class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Change avatar">
                                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                            class="path2"></span></i>
                                                    <input type="file" name="socials[0][image]"
                                                        accept=".png, .jpg, .jpeg" />
                                                    <input type="hidden" name="socials[0][image_remove]" />
                                                </label>
                                                <span
                                                    class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Cancel avatar">
                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                </span>
                                                <span
                                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                    data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Remove avatar">
                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="social-name-0" class="form-label form-input-labels">Social
                                            Name</label>
                                        <input type="text" class="form-control form-inputs-field"
                                            placeholder="Enter social name" id="social-name-0" name="socials[0][name]"
                                            value="">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="social-link-0" class="form-label form-input-labels">Social
                                            Link</label>
                                        <input type="text" class="form-control form-inputs-field"
                                            placeholder="Enter social link" id="social-link-0" name="socials[0][link]"
                                            value="">
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Save</button>
                </div>
            </form>
        </div>
    </div>
</div><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/profile_settings/modal/edit-social-modal.blade.php ENDPATH**/ ?>