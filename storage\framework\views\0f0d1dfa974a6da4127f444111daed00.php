<div class="form-card">
    <div class="row">
        <div class="col-12 mb-3">
            <h2 class="fs-title">Add your opening hours</h2>
            <p>Set standard opening hours to show on your profile page, these hours do not impact your calendar
                availability</p>
        </div>

        <div class="gray-card mb-10">
            <?php
                $daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                $userOpeningHours = auth()->user()->openingHours->keyBy('day');
            ?>

            <?php $__currentLoopData = $daysOfWeek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $openingHour = $userOpeningHours->get($day); // Get the user's opening hour for the current day
                    $isChecked = $openingHour && $openingHour->type === 'open'; // Check if the day is marked as "open"
                    $start = $isChecked ? $openingHour->open : ''; // Get the start time if open
                    $end = $isChecked ? $openingHour->close : ''; // Get the end time if open
                ?>
                <div class="time-picker-calendar2 mb-8">
                    <label class="days">
                        <input type="checkbox" value="<?php echo e($day); ?>" name="availability[<?php echo e($index); ?>][day]"
                            <?php echo e($isChecked ? 'checked' : ''); ?>>
                        <span class="checkmark"><?php echo e($day); ?></span>
                    </label>
                    <div class="time-picker-range2">
                        <div class="checked-time">
                            <div class="start-time1">
                                <input type="text" name="availability[<?php echo e($index); ?>][start]"
                                    class="flatpickr-time mb-0 no_validate" placeholder="Start Time"
                                    value="<?php echo e($start); ?>">
                            </div>
                            <p class="mb-0"> - </p>
                            <div class="end-time1">
                                <input type="text" name="availability[<?php echo e($index); ?>][end]"
                                    class="flatpickr-time mb-0 no_validate" placeholder="End Time"
                                    value="<?php echo e($end); ?>">
                            </div>
                        </div>

                        <div class="closed-time">
                            <p> Closed</p>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="d-flex justify-content-between">
            <h5>List of Holidays</h5>
            <label class="days holiday-list"> <input type="checkbox" class="select-all">
                <span class="checkmark">Select All</span>
            </label>
        </div>

        <div class="gray-card  mb-8">
            <?php $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $checked_date = auth()->user()->holidays->pluck('holiday_id')->contains($holiday->id);
                    $userHoliday = auth()->user()->holidays->where('holiday_id', $holiday->id)->first();
                    $startTime = $userHoliday && $userHoliday->start_time ? \Carbon\Carbon::parse($userHoliday->start_time)->format('h:i A') : '';
                    $endTime = $userHoliday && $userHoliday->end_time ? \Carbon\Carbon::parse($userHoliday->end_time)->format('h:i A') : '';
                    $hasTimeData = $userHoliday && ($userHoliday->start_time || $userHoliday->end_time);
                ?>

                <div class="time-picker-calendar">
                    <div class="d-flex justify-content-between align-items-center">
                        <label class="days"><input type="checkbox" name="holidays[<?php echo e($index); ?>][holiday_id]"
                                value="<?php echo e($holiday->id); ?>" class="day-checkbox" <?php echo e($checked_date ? 'checked' : ''); ?>>
                            <span class="checkmark "><?php echo e($holiday->name ?? ''); ?></span>
                            <input type="hidden" name="holidays[<?php echo e($index); ?>][name]"
                                value="<?php echo e($holiday->name); ?>">
                            <input type="hidden" name="holidays[<?php echo e($index); ?>][date]"
                                value="<?php echo e($holiday->date); ?>">
                        </label>
                        <p><?php echo e($holiday->date ?? ''); ?></p>
                    </div>
                    <div class="start-time " style="display: <?php echo e(($checked_date && $hasTimeData) ? 'flex' : 'none'); ?>;">
                        <div class="d-flex gap-10 justify-content-center">
                            <input type="text" class="flatpickr-time no_validate"
                                name="holidays[<?php echo e($index); ?>][start_time]" placeholder="Start Time" value="<?php echo e($startTime); ?>">
                            <p> - </p>
                            <input type="text" class="flatpickr-time no_validate"
                                name="holidays[<?php echo e($index); ?>][end_time]" placeholder="End Time" value="<?php echo e($endTime); ?>">
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


            
            <?php $__empty_1 = true; $__currentLoopData = auth()->user()->customHolidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customHoliday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="time-picker-calendar custom-holiday-div">
                    <div class="d-flex justify-content-between align-items-center">
                        <label class="days">
                            <input type="checkbox" class="day-checkbox" name="custom_holidays[<?php echo e($loop->index); ?>]" <?php echo e(isset($customHoliday->start_time, $customHoliday->end_time) ? 'checked' : ''); ?>>
                            <span class="checkmark"><?php echo e($customHoliday->name); ?></span>
                            <input type="hidden" name="custom_holidays[<?php echo e($loop->index); ?>][name]" value="<?php echo e($customHoliday->name); ?>">
                            <input type="hidden" name="custom_holidays[<?php echo e($loop->index); ?>][date]" value="<?php echo e($customHoliday->date); ?>">
                        </label>
                        <p><?php echo e($customHoliday->date); ?></p>
                    </div>
                    <button class="delete-holiday btn btn-sm btn-danger mb-8">Delete</button>
                    <div class="start-time" style="display: <?php echo e(isset($customHoliday->start_time, $customHoliday->end_time) ? 'flex' : 'none'); ?>;">
                        <div class="d-flex gap-10">
                            <input type="text" class="flatpickr-time no_validate flatpickr-input"
                                placeholder="Select Time" name="custom_holidays[<?php echo e($loop->index); ?>][start_time]" value="<?php echo e($customHoliday->start_time ? \Carbon\Carbon::parse($customHoliday->start_time)->format('h:i A') : ''); ?>">
                            <p> - </p>
                            <input type="text" class="flatpickr-time no_validate flatpickr-input"
                                placeholder="Select Time" name="custom_holidays[<?php echo e($loop->index); ?>][end_time]" value="<?php echo e($customHoliday->end_time ? \Carbon\Carbon::parse($customHoliday->end_time)->format('h:i A') : ''); ?>">
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>

            <?php endif; ?>
            
            <button type="button" class="add-custom-holiday-btn">+ Add Custom Holiday</button>
            <!-- Modal -->
            <div id="customHolidayModal" class="HolidayModal modal" style="display:none;">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h4 class="text-center mb-7">Add Custom Holiday</h4>
                    <label>Holiday Name:</label>
                    <input class="no_validate" type="text" id="customHolidayName" placeholder="Enter holiday name">
                    <label>Date:</label>
                    <input class="no_validate" type="text" id="customHolidayDate" placeholder="Select date">
                    <button type="button" class="blue-btn py-3" id="saveCustomHoliday">Save</button>
                </div>
            </div>
        </div>
        <p>Select holidays to show your availability. </p>
    </div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/templates/professional-acc-stepper/step4.blade.php ENDPATH**/ ?>