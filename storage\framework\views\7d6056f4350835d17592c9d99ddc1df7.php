<?php $__env->startPush('css'); ?>
    <style>
        .professional-swipper .swiper-slide {
            width: auto !important;
            flex-shrink: 0;
        }

        .professional-swipper .nav-item {
            margin: 0;
        }

        .professional-swipper .professional-tab {
            white-space: nowrap;
            min-width: fit-content;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section class="service padding">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-md-12 d-flex justify-content-between">
                    <h1 class="sora black  fs-34 semi_bold">Services</h1>
                    <div class="search-bar d-flex align-items-center">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <input class="search-servies" type="text" placeholder="Search" name="search-servies">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="position-relative">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                            <!-- Swiper -->
                            <div class="swiper mySwiper professional-swipper ">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link  professional-tab" id="pills-home-tab"
                                                data-bs-toggle="pill" data-bs-target="#pills-home" type="button"
                                                role="tab" aria-controls="pills-home" aria-selected="true">Personal
                                                Trainers
                                            </button>
                                        </li>
                                    </div>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="swiper-slide" style="width: 100%;">
                                            <li class="nav-item" role="presentation">
                                                <button
                                                    class="nav-link <?php echo e($category->slug == $active_category ? 'active' : ''); ?> professional-tab category-tab-btn"
                                                    id="pills-category-<?php echo e($category->slug); ?>-tab"
                                                    data-category-name="<?php echo e($category->slug); ?>" data-bs-toggle="pill"
                                                    data-bs-target="#pills-profile" type="button" role="tab"
                                                    aria-controls="pills-profile"
                                                    aria-selected="<?php echo e($category->slug == $active_category ? 'true' : 'false'); ?>">
                                                    <?php echo e($category->name); ?>

                                                </button>
                                            </li>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </ul>
                    </div>

                    <div id="subcategory-service-container">
                        
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php echo $__env->make('website.template.modal.filter-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.templates.modal.add-service-details-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        // get services
        const getServices = (category, subcategory) => {
            $.ajax({
                url: "<?php echo e(route('filter_services')); ?>",
                type: "GET",
                data: {
                    category: category,
                    subcategory: subcategory
                },
                success: function(response) {
                    if (response.status == true) {
                        const $temp = $('<div>').html(response.data.page);
                        $('#subcategory-service-container').html(response.data.page);
                        // Execute each <script> from the loaded view
                        $temp.find('script').each(function() {
                            $.globalEval(this.innerHTML);
                        });
                        // Update the browser's URL without reloading the page
                        let newUrl = `/services-all/${category}`;
                        if (response.data.subcategory) {
                            newUrl += `/${response.data.subcategory}`;
                        }
                        window.history.pushState({
                            path: newUrl
                        }, '', newUrl);
                    } else {
                        $('#subcategory-service-container').html(response.message);
                    }

                }
            });
        }
        // get services end

        $(document).ready(function() {
            // initial page reload

            getServices('<?php echo e($active_category); ?>', '<?php echo e($active_subcategory); ?>');
            var serviceSwiper = new Swiper(".professional-swipper", {
                slidesPerView: 'auto',
                spaceBetween: 10,
                freeMode: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 5
                    },
                    640: {
                        slidesPerView: 3,
                        spaceBetween: 10
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 10
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 10
                    }
                }
            });
            $(document).on('click', '.category-tab-btn,.subcategory-tab-btn', function() {
                let category = $(this).data("category-name");
                let subcategory = $(this).data("subcategory-name");

                // Update active states for category tabs
                if ($(this).hasClass('category-tab-btn')) {
                    $('.category-tab-btn').removeClass('active').attr('aria-selected', 'false');
                    $(this).addClass('active').attr('aria-selected', 'true');
                }

                // Update active states for subcategory tabs
                if ($(this).hasClass('subcategory-tab-btn')) {
                    $('.subcategory-tab-btn').removeClass('active').attr('aria-selected', 'false');
                    $(this).addClass('active').attr('aria-selected', 'true');
                }

                getServices(category, subcategory);
            });
        });
    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/website/service.blade.php ENDPATH**/ ?>