<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard ">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12 d-flex justify-content-between">
                    <div>
                        <h6 class="semi_bold sora black">Analytics</h6>
                        <p class="fs-14 normal sora light-black">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <!-- category -->
                    <div class="search_box select-box">
                        <select class="search_input">
                            <option value="select">Select</option>
                            <option value=" Weekly"> Weekly</option>
                            <option value="Monthly">Monthly</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper">
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue ">
                                    <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Sales
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="642.39" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div class="fs-12 w-700 green green-box">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-green ">
                                    <?php echo $__env->make('svg.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    From last month
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="574.34" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div class="fs-12 w-700 green green-box">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-purple">
                                    <?php echo $__env->make('svg.earning', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Sales to date
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="350.4" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div class="fs-12 w-700 green green-box">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="#">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-orange">
                                    <?php echo $__env->make('svg.booking', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Number of Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="2935">
                                </p>
                            </div>
                            <div class="card-footer w-50">
                                <div class="fs-12 w-700 green green-box">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-8">
                    <div class="  card-box">
                        <p class="black sora semi_bold">Peak Booking Time</p>
                        <canvas id="popularServicesChart"></canvas>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="  card-box">
                        <p class="black sora semi_bold">Peak Booking Time</p>
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="chart-container-booking">
                                <canvas id="bookingChart" style="width:300px ; height: 400px;  "></canvas>
                            </div>
                            <div id="chartLegend" style="margin-left: 30px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
   <!-- booking-chart -->
    <script>
        const ctx = document.getElementById('popularServicesChart').getContext('2d');
        const data = {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Haircuts & Styling',
                data: [140, 210, 160, 240, 300, 300, 350],
                borderColor: '#3B82F6',
                backgroundColor: function (context) {
                    const chart = context.chart;
                    const { ctx, chartArea } = chart;
                    if (!chartArea) return null;

                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.00)');
                    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.15)');
                    return gradient;
                },
                fill: true,
                tension: 0.5,
                pointBackgroundColor: 'blue',
                pointBorderColor: 'transparent',
                pointRadius: 1,
                pointHoverRadius: 7,
            },
            {
                label: 'Hair Coloring',
                data: [100, 150, 220, 200, 220, 180, 240],
                borderColor: '#0BC688',
                backgroundColor: function (context) {
                    const chart = context.chart;
                    const { ctx, chartArea } = chart;
                    if (!chartArea) return null;

                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.00)');
                    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.15)');
                    return gradient;
                },
                fill: false,
                tension: 0.5,
                pointBackgroundColor: '#0BC688',
                pointBorderColor: 'transparent',
                pointRadius: 0,
                pointHoverRadius: 7,
            },
            {
                label: 'Hair Trimming',
                data: [110, 170, 260, 250, 260, 270, 250],
                borderColor: '#F1962D',
                backgroundColor: function (context) {
                    const chart = context.chart;
                    const { ctx, chartArea } = chart;
                    if (!chartArea) return null;

                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.00)');
                    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.15)');
                    return gradient;
                },
                fill: false,
                tension: 0.5,
                pointBackgroundColor: '#F1962D',
                pointBorderColor: 'transparent',
                pointRadius: 0,
                pointHoverRadius: 7,
            }
            ]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        align: 'start',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20,
                            boxWidth: 5,
                            boxHeight: 5,
                        }
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function (context) {
                                return context.raw + ' Bookings';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12,
                                weight: '400',
                                opacity: 0.6,
                            },
                            color: '#363636'
                        }

                    },
                    y: {
                        grid: {
                            display: true
                        },
                        beginAtZero: true,
                        min: 50,
                        max: 350,
                        ticks: {
                            font: {
                                size: 12,
                                weight: 400,
                            },
                            color: '#363636',
                            stepSize: 50
                        }
                    }
                }
            }
        };

        const popularServicesChart = new Chart(ctx, config);
    </script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/analytics.blade.php ENDPATH**/ ?>