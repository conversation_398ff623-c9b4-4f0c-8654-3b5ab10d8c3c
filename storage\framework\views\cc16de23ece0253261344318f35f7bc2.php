<?php $__env->startPush('css'); ?>
    <!-- Include Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        /* Trustpilot Section Styling */
        .trustpilot-logo {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            transition: transform 0.3s ease;
        }

        .trustpilot-logo:hover {
            transform: scale(1.05);
        }

        .trustpilot-preview-placeholder {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .trustpilot-stars i {
            font-size: 1.2rem;
            margin-right: 2px;
        }

        .form-select-field {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
        }

        .alert-success {
            background-color: #d1edff;
            border-color: #0ea5e9;
            color: #0369a1;
        }

        .card-box {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .trustpilot-widget {
            min-height: 100px;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Animation for form elements */
        .form-inputs-field:focus,
        .form-select-field:focus {
            border-color: #00b67a;
            box-shadow: 0 0 0 0.2rem rgba(0, 182, 122, 0.25);
        }

        /* Custom switch styling */
        .form-check-input:checked {
            background-color: #00b67a;
            border-color: #00b67a;
        }

        .form-check-input:focus {
            border-color: #00b67a;
            box-shadow: 0 0 0 0.25rem rgba(0, 182, 122, 0.25);
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12">
                    <h6 class="sora black">Settings</h6>
                    <p class="fs-14 sora normal light-black">Lorem ipsum dolor sit amet consectetur. </p>
                </div>
            </div>
            <div class="row row-gap-5">
                <div class="col-md-4">
                    <div class="card-box">
                        <ul class="nav nav-pills flex-column setting-nav gap-3" id="pills-tab" role="tablist">

                            <?php if(Auth::user()->hasAnyRole(['individual', 'business', 'admin', 'professional'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active setting-tabs" id="change-password-tab"
                                        data-bs-toggle="pill" data-bs-target="#pills-change-password" type="button"
                                        role="tab" aria-controls="pills-change-password" aria-selected="true">Change
                                        Password
                                    </button>
                                </li>
                                <?php if(Auth::user()->hasRole(['admin'])): ?>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link setting-tabs" id="account-tab" data-bs-toggle="pill"
                                            data-bs-target="#pills-account" type="button" role="tab"
                                            aria-controls="pills-account" aria-selected="true">Account
                                        </button>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if(Auth::user()->hasAnyRole(['individual', 'business'])): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link setting-tabs" id="stripe-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-stripe" type="button" role="tab"
                                        aria-controls="pills-stripe" aria-selected="true">Stripe Configuration
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link setting-tabs" id="trustpilot-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-trustpilot" type="button" role="tab"
                                        aria-controls="pills-trustpilot" aria-selected="true">Trust Pilot Configuration
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link setting-tabs" id="google-calendar-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-google-calendar" type="button" role="tab"
                                        aria-controls="pills-google-calendar" aria-selected="true">Google Calendar
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link setting-tabs" id="outlook-calendar-tab" data-bs-toggle="pill"
                                        data-bs-target="#pills-outlook-calendar" type="button" role="tab"
                                        aria-controls="pills-outlook-calendar" aria-selected="true">Outlook Calendar
                                    </button>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-change-password" role="tabpanel"
                            aria-labelledby="change-password-tab" tabindex="0">
                            <div class="card card-box p-0">
                                <form action="<?php echo e(route('update.password')); ?>" method="POST" id="update-password-form">
                                    <?php echo csrf_field(); ?>
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Change Password</p>
                                        <button type="submit"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Save
                                            Changes</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 position-relative">
                                                <label for="current-password" class="form-label form-input-labels">Current
                                                    Password</label>
                                                <input type="password"
                                                    class="form-control form-inputs-field password-toggle"
                                                    placeholder="Enter current password" id="current-password"
                                                    name="current_password">
                                                <span toggle=".password-toggle"
                                                    class="fa  fa-eye-slash  field-icon toggle-password"></span>
                                            </div>
                                            <div class="col-md-12 position-relative">
                                                <label for="new-password" class="form-label form-input-labels">New
                                                    Password</label>
                                                <input type="password"
                                                    class="form-control form-inputs-field password-toggle"
                                                    placeholder="Enter new password" id="new-password"
                                                    name="new_password">
                                                <span toggle=".password-toggle"
                                                    class="fa  fa-eye-slash  field-icon toggle-password"></span>
                                            </div>
                                            <div class="col-md-12 position-relative">
                                                <label for="confirm-password" class="form-label form-input-labels">Confirm
                                                    Password</label>
                                                <input type="password"
                                                    class="form-control form-inputs-field password-toggle"
                                                    placeholder="Confirm new password" id="confirm-password"
                                                    name="confirm_password">
                                                <span toggle=".password-toggle"
                                                    class="fa  fa-eye-slash  field-icon toggle-password"></span>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </form>
                            </div>

                        </div>
                        <?php if(Auth::user()->hasRole(['admin'])): ?>
                            <div class="tab-pane fade" id="pills-account" role="tabpanel" aria-labelledby="account-tab"
                                tabindex="0">
                                <div class="card">
                                    <div class="card-header align-items-center py-5">
                                        <p class="sora black fs-16 semi_bold">Account</p>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 ">
                                                <div class="Image-input_holder settings-image-input  mb-10">
                                                    <div class="image-input image-input-empty" data-kt-image-input="true">
                                                        <div class="image-input image-input-outline"
                                                            data-kt-image-input="true">
                                                            <div class="image-input-wrapper w-125px h-125px"></div>
                                                            <label class="dark-green-btn fs-14 regular pt-9"
                                                                data-kt-image-input-action="change">
                                                                <span class="pe-3 fs-14 medium mb-10 deep-blue"> Edit Image
                                                                </span>
                                                                <input type="file" name="avatar"
                                                                    accept=".png, .jpg, .jpeg" />
                                                                <input type="hidden" name="avatar_remove" />
                                                                <p class="fs-14 medium pt-4 light-black"> At least 500x500
                                                                    px
                                                                    recommended.
                                                                    JPG or PNG is allowed</p>
                                                            </label>
                                                            <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                                data-kt-image-input-action="cancel"
                                                                data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                title="Cancel avatar"> <i class="fas fa-times fa-5"></i>
                                                            </a>
                                                            <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                                data-kt-image-input-action="remove"> Remove </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6 ">
                                                <label for="email" class="form-label form-input-labels">Email
                                                    Address</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="email" name="email"
                                                    value="<EMAIL>" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="tab-pane fade" id="pills-stripe" role="tabpanel" aria-labelledby="stripe-tab"
                            tabindex="0">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Stripe Configuration</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-8 justify-content-center">
                                        <div class="col-md-6  col-xxl-5 p-0">
                                            <div class="d-flex flex-column justify-content-center gap-3">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/stripe.svg"
                                                    class="h-100 w-100 object-fit-contain rounded-pill top-rated-image"
                                                    alt="card-image" />
                                                <p class="text-center regular light-black">To start receiving payments for
                                                    your
                                                    sales, you'll need to connect your Stripe account.</p>
                                                <ul class="fs-14 light-black nomal">
                                                    <li>
                                                        Secure and instant payouts directly to your bank
                                                    </li>
                                                    <li>Track and manage your earning seamlessly</li>
                                                    <li>
                                                        Essential for completing sales and withdrawals.</li>
                                                </ul>
                                                <p class="text-center fs-14 normal light-black">Once connected, you'll be
                                                    able to receive payouts for your sales.</p>
                                                <p class="text-center fs-14 normal light-black">⚠️️️️ Important: You won’t
                                                    be able to receive payments until your Stripe account is connected.</p>
                                                <a href="<?php echo e(route('stripe.connect')); ?>" class="add-btn w-100 text-center">
                                                    Configure Your Stripe
                                                </a>

                                            </div>
                                        </div>
                                        <div class="col-md-12 ">
                                            <div class="card card-box justify-content-center align-items-center">
                                                <p class="fs-16 sora black semi_bold ">How to setup Stripe?</p>

                                                <iframe width="560" height="315"
                                                    src="https://www.youtube.com/embed/WFi9dRhmGFo?si=nQuTSof8_67kxbAK"
                                                    title="YouTube video player" frameborder="0"
                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; "
                                                    referrerpolicy="strict-origin-when-cross-origin"
                                                    allowfullscreen></iframe>

                                            </div>
                                        </div>
                                        <?php if(Auth::user()->stripe_enabled): ?>
                                            <div class="col-md-12">
                                                <p
                                                    class="form-control form-inputs-field p-0 px-2 d-flex justify-content-between align-items-center">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/stripe.svg"
                                                        class="h-50px w-50px object-fit-contain" alt="card-image" />
                                                    <span class="opacity-6">Account Configured!</span>
                                                </p>
                                                <form action="<?php echo e(route('stripe.disconnect')); ?>" method="POST"
                                                    style="display:inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="add-btn"> Remove Stripe Account</button>
                                                </form>
                                                <p class="light-black fs-14 normal my-5"> ⚠️️️️ Important: Once
                                                    disconnected,
                                                    you won't be able to receive payouts for sales until you reconnect a
                                                    Stripe
                                                    account.
                                                    If you have pending payments, ensure they are processed before unlinking
                                                    your account.</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="pills-trustpilot" role="tabpanel"
                            aria-labelledby="trustpilot-tab" tabindex="0">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Trustpilot Configuration</p>
                                    <?php if(Auth::user()->trustpilotAccount): ?>
                                        <form action="<?php echo e(route('trustpilot.disconnect')); ?>" method="POST"
                                            style="display:inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit"
                                                class="fs-16 text-danger semi_bold bg-transparent p-0 border-0">
                                                Disconnect Account
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-8 justify-content-center">
                                        <div class="col-md-6 col-xxl-5 p-0">
                                            <div class="d-flex flex-column justify-content-center gap-3">
                                                <div class="trustpilot-logo-container text-center mb-3">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/trustpilot-logo.png"
                                                        class="trustpilot-logo" alt="Trustpilot Logo"
                                                        style="max-height: 80px; width: auto;" />
                                                </div>
                                                <p class="text-center regular light-black mb-3">
                                                    Build trust with your customers by showcasing your Trustpilot
                                                    reviews and ratings.
                                                </p>
                                                <ul class="fs-14 light-black normal mb-4">
                                                    <li class="mb-2">
                                                        <i class="fas fa-star text-warning me-2"></i>
                                                        Display authentic customer reviews
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-shield-alt text-success me-2"></i>
                                                        Build credibility and trust
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-chart-line text-primary me-2"></i>
                                                        Increase conversion rates
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-users text-info me-2"></i>
                                                        Attract more customers
                                                    </li>
                                                </ul>

                                                <?php if(!Auth::user()->trustpilotAccount): ?>
                                                    <p class="text-center fs-14 normal light-black mb-4">
                                                        Connect your Trustpilot account to start displaying reviews on your
                                                        profile.
                                                    </p>
                                                    <a href="<?php echo e(route('trustpilot.connect')); ?>"
                                                        class="add-btn w-100 text-center">
                                                        <i class="fas fa-link me-2"></i>Connect to Trustpilot
                                                    </a>
                                                <?php else: ?>
                                                    <div class="alert alert-success d-flex align-items-center"
                                                        role="alert">
                                                        <i class="fas fa-check-circle me-3 fs-5"></i>
                                                        <div>
                                                            <strong>Trustpilot Connected!</strong><br>
                                                            <small>Business Unit ID:
                                                                <?php echo e(Auth::user()->trustpilotAccount->business_unit_id); ?></small>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <?php if(Auth::user()->trustpilotAccount): ?>
                                            <div class="col-md-12">
                                                <div class="card card-box">
                                                    <div class="card-header">
                                                        <h5 class="sora black fs-16 semi_bold mb-0">
                                                            <i class="fas fa-cog me-2"></i>Account Details
                                                        </h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <p class="fs-14 light-black mb-2">
                                                                    <strong>Business Unit ID:</strong><br>
                                                                    <code><?php echo e(Auth::user()->trustpilotAccount->business_unit_id); ?></code>
                                                                </p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <p class="fs-14 light-black mb-2">
                                                                    <strong>Connection Status:</strong><br>
                                                                    <span class="badge bg-success">Connected</span>
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div class="row mt-3">
                                                            <div class="col-md-12">
                                                                <p class="fs-14 light-black mb-0">
                                                                    <i class="fas fa-info-circle me-2"></i>
                                                                    Your Trustpilot account is successfully connected.
                                                                    Reviews will be displayed on your public profile.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Preview Section -->
                                        <div class="col-md-12">
                                            <div class="card card-box">
                                                <div class="card-header">
                                                    <h5 class="sora black fs-16 semi_bold mb-0">
                                                        <i class="fas fa-eye me-2"></i>Preview
                                                    </h5>
                                                </div>
                                                <div class="card-body text-center">
                                                    <?php if(Auth::user()->trustpilotAccount): ?>
                                                        <div class="trustpilot-widget" data-locale="en-US"
                                                            data-template-id="5419b6a8b0d04a076446a9ad"
                                                            data-businessunit-id="<?php echo e(Auth::user()->trustpilotAccount->business_unit_id); ?>"
                                                            data-style-height="24px" data-style-width="100%"
                                                            data-theme="light">
                                                            <div class="trustpilot-preview-placeholder p-4">
                                                                <div
                                                                    class="d-flex align-items-center justify-content-center mb-3">
                                                                    <div class="trustpilot-stars me-3">
                                                                        <i class="fas fa-star text-warning"></i>
                                                                        <i class="fas fa-star text-warning"></i>
                                                                        <i class="fas fa-star text-warning"></i>
                                                                        <i class="fas fa-star text-warning"></i>
                                                                        <i class="fas fa-star text-warning"></i>
                                                                    </div>
                                                                    <span class="fs-14 fw-bold">4.8 out of 5</span>
                                                                </div>
                                                                <p class="fs-14 text-muted mb-0">Based on your Trustpilot
                                                                    reviews</p>
                                                                <small class="text-success">
                                                                    <i class="fas fa-check-circle me-1"></i>
                                                                    Connected to Business Unit:
                                                                    <?php echo e(Auth::user()->trustpilotAccount->business_unit_id); ?>

                                                                </small>
                                                            </div>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="trustpilot-preview-placeholder p-4">
                                                            <div
                                                                class="d-flex align-items-center justify-content-center mb-3">
                                                                <div class="trustpilot-stars me-3">
                                                                    <i class="fas fa-star text-muted"></i>
                                                                    <i class="fas fa-star text-muted"></i>
                                                                    <i class="fas fa-star text-muted"></i>
                                                                    <i class="fas fa-star text-muted"></i>
                                                                    <i class="fas fa-star text-muted"></i>
                                                                </div>
                                                                <span class="fs-14 text-muted">No reviews yet</span>
                                                            </div>
                                                            <p class="fs-14 text-muted mb-0">Connect your account to
                                                                display reviews</p>
                                                            <small class="text-muted">
                                                                <i class="fas fa-info-circle me-1"></i>
                                                                Preview will show your actual reviews once connected
                                                            </small>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Help Section -->
                                        <div class="col-md-12">
                                            <div class="card card-box">
                                                <div class="card-header">
                                                    <h5 class="sora black fs-16 semi_bold mb-0">
                                                        <i class="fas fa-question-circle me-2"></i>How to Setup
                                                        Trustpilot
                                                    </h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold mb-3">How OAuth Connection Works:</h6>
                                                            <ol class="fs-14 light-black">
                                                                <li class="mb-2">Create a Trustpilot business account
                                                                </li>
                                                                <li class="mb-2">Verify your business domain</li>
                                                                <li class="mb-2">Click "Connect to Trustpilot" button
                                                                </li>
                                                                <li class="mb-2">Authorize access in Trustpilot</li>
                                                                <li class="mb-2">Your account will be automatically
                                                                    connected</li>
                                                            </ol>
                                                            <div class="alert alert-info mt-3">
                                                                <i class="fas fa-shield-alt me-2"></i>
                                                                <small>OAuth provides secure authentication without sharing
                                                                    passwords.</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <?php if(!Auth::user()->trustpilotAccount): ?>
                                                                <div class="mt-3">
                                                                    <h6 class="fw-bold mb-2">Ready to Connect?</h6>
                                                                    <p class="fs-14 light-black mb-2">
                                                                        Make sure you have a Trustpilot business account,
                                                                        then click the connect button above.
                                                                    </p>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Google Calendar Tab -->
                        <div class="tab-pane fade" id="pills-google-calendar" role="tabpanel"
                            aria-labelledby="google-calendar-tab" tabindex="0">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Google Calendar Configuration</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-8 justify-content-center">
                                        <div class="col-md-6 col-xxl-5 p-0">
                                            <div class="d-flex flex-column justify-content-center gap-3">
                                                <div class="text-center mb-3">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/Google_Logo.svg"
                                                        class="h-100 w-100 object-fit-contain" alt="Google Calendar"
                                                        style="max-height: 80px;" />
                                                </div>
                                                <p class="text-center regular light-black mb-3">
                                                    Sync your appointments and events with Google Calendar to manage your
                                                    schedule seamlessly.
                                                </p>
                                                <ul class="fs-14 light-black normal mb-4">
                                                    <li class="mb-2">
                                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                                        Automatic event synchronization
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-clock text-success me-2"></i>
                                                        Real-time schedule updates
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-bell text-warning me-2"></i>
                                                        Smart notifications and reminders
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-users text-info me-2"></i>
                                                        Easy appointment booking for clients
                                                    </li>
                                                </ul>
                                                <p class="text-center fs-14 normal light-black mb-4">
                                                    Connect your Google Calendar to enable seamless appointment management.
                                                </p>
                                                <a type="button" class="add-btn w-100 text-center"
                                                    id="configure-google-calendar"
                                                    href="<?php echo e(route('google.calendar.connect')); ?>">
                                                    <i class="fab fa-google me-2"></i>Configure Calendar
                                                </a>
                                            </div>
                                        </div>

                                        <!-- Help Section -->
                                        <div class="col-md-12">
                                            <div class="card card-box">
                                                <div class="card-header">
                                                    <h5 class="sora black fs-16 semi_bold mb-0">
                                                        <i class="fas fa-question-circle me-2"></i>How to Setup Google
                                                        Calendar
                                                    </h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold mb-3">Setup Steps:</h6>
                                                            <ol class="fs-14 light-black">
                                                                <li class="mb-2">Click "Configure Calendar" button</li>
                                                                <li class="mb-2">Sign in to your Google account</li>
                                                                <li class="mb-2">Grant calendar access permissions</li>
                                                                <li class="mb-2">Select calendars to sync</li>
                                                                <li class="mb-2">Configure sync preferences</li>
                                                            </ol>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold mb-3">Benefits:</h6>
                                                            <ul class="fs-14 light-black">
                                                                <li class="mb-2">Two-way synchronization</li>
                                                                <li class="mb-2">Automatic conflict detection</li>
                                                                <li class="mb-2">Mobile and desktop access</li>
                                                                <li class="mb-2">Shared calendar support</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Outlook Calendar Tab -->
                        <div class="tab-pane fade" id="pills-outlook-calendar" role="tabpanel"
                            aria-labelledby="outlook-calendar-tab" tabindex="0">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Outlook Calendar Configuration</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-8 justify-content-center">
                                        <div class="col-md-6 col-xxl-5 p-0">
                                            <div class="d-flex flex-column justify-content-center gap-3">
                                                <div class="text-center mb-3">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/outlook.svg"
                                                        class="h-100 w-100 object-fit-contain" alt="Outlook Calendar"
                                                        style="max-height: 80px;" />
                                                </div>
                                                <p class="text-center regular light-black mb-3">
                                                    Integrate with Microsoft Outlook Calendar to streamline your appointment
                                                    scheduling and management.
                                                </p>
                                                <ul class="fs-14 light-black normal mb-4">
                                                    <li class="mb-2">
                                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                                        Outlook calendar integration
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-sync text-success me-2"></i>
                                                        Bi-directional synchronization
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-envelope text-info me-2"></i>
                                                        Email notifications and invites
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fas fa-users text-warning me-2"></i>
                                                        Team calendar sharing
                                                    </li>
                                                </ul>
                                                <p class="text-center fs-14 normal light-black mb-4">
                                                    Connect your Outlook account to enable calendar
                                                    synchronization.
                                                </p>
                                                <button type="button" class="add-btn w-100 text-center"
                                                    id="configure-outlook-calendar">
                                                    <i class="fab fa-microsoft me-2"></i>Configure Calendar
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Help Section -->
                                        <div class="col-md-12">
                                            <div class="card card-box">
                                                <div class="card-header">
                                                    <h5 class="sora black fs-16 semi_bold mb-0">
                                                        <i class="fas fa-question-circle me-2"></i>How to Setup Outlook
                                                        Calendar
                                                    </h5>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold mb-3">Setup Steps:</h6>
                                                            <ol class="fs-14 light-black">
                                                                <li class="mb-2">Click "Configure Calendar" button</li>
                                                                <li class="mb-2">Sign in with Microsoft account</li>
                                                                <li class="mb-2">Authorize calendar permissions</li>
                                                                <li class="mb-2">Choose calendars to integrate</li>
                                                                <li class="mb-2">Set synchronization preferences</li>
                                                            </ol>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6 class="fw-bold mb-3">Features:</h6>
                                                            <ul class="fs-14 light-black">
                                                                <li class="mb-2">Office 365 integration</li>
                                                                <li class="mb-2">Teams meeting support</li>
                                                                <li class="mb-2">Exchange server compatibility</li>
                                                                <li class="mb-2">Enterprise security features</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        // Wait for jQuery validate to load
        function waitForValidate() {
            if (typeof $.fn.validate !== 'undefined') {
                console.log('jQuery Validate loaded successfully');
                initializeValidation();
            } else {
                console.log('Waiting for jQuery Validate...');
                setTimeout(waitForValidate, 100);
            }
        }

        function initializeValidation() {
            // Password toggle functionality
            $(".toggle-password").click(function() {
                console.log('Password toggle clicked');
                const input = $(this).siblings("input");
                const inputType = input.attr("type") === "password" ? "text" : "password";
                input.attr("type", inputType);

                $(this).toggleClass("fa-eye-slash fa-eye");
            });
            // Add custom validation method for current password
            $.validator.addMethod("checkCurrentPassword", function(value, element) {
                var isValid = false;
                if (value) {
                    $.ajax({
                        url: "<?php echo e(route('check.current.password')); ?>",
                        type: "POST",
                        async: false,
                        data: {
                            _token: "<?php echo e(csrf_token()); ?>",
                            current_password: value
                        },
                        success: function(response) {
                            isValid = response.valid;
                        },
                        error: function(xhr, status, error) {
                            console.log('Error checking password:', error);
                            isValid = false;
                        }
                    });
                }
                return isValid;
            }, "Current password is incorrect");

            // Form validation
            if (typeof $.fn.validate !== 'undefined') {
                $('#update-password-form').validate({
                    rules: {
                        current_password: {
                            required: true,
                            checkCurrentPassword: true
                        },
                        new_password: {
                            required: true,
                            minlength: 8
                        },
                        confirm_password: {
                            required: true,
                            equalTo: "#new-password"
                        }
                    },
                    messages: {
                        current_password: {
                            required: "Please enter your current password"
                        },
                        new_password: {
                            required: "Please enter your new password",
                            minlength: "Your new password must be at least 8 characters long"
                        },
                        confirm_password: {
                            required: "Please confirm your new password",
                            equalTo: "Please enter the same password as above"
                        }
                    },
                    errorElement: 'span',
                    errorClass: 'text-danger',
                    errorPlacement: function(error, element) {
                        error.addClass('invalid-feedback');
                        element.closest('.position-relative').append(error);
                    },
                    highlight: function(element, errorClass, validClass) {
                        $(element).addClass('is-invalid');
                    },
                    unhighlight: function(element, errorClass, validClass) {
                        $(element).removeClass('is-invalid');
                    },
                    submitHandler: function(form) {
                        form.submit();
                    }
                });
                // Real-time validation for current password
                $('#current-password').on('blur', function() {
                    $(this).valid();
                });
            } else {
                console.error('jQuery Validate plugin not loaded');
                // Fallback validation
                $('#update-password-form').on('submit', function(e) {
                    var isValid = true;
                    var currentPassword = $('#current-password').val();
                    var newPassword = $('#new-password').val();
                    var confirmPassword = $('#confirm-password').val();
                    if (!currentPassword) {
                        alert('Please enter your current password');
                        isValid = false;
                    }
                    if (!newPassword || newPassword.length < 8) {
                        alert('Please enter a new password with at least 8 characters');
                        isValid = false;
                    }
                    if (newPassword !== confirmPassword) {
                        alert('Passwords do not match');
                        isValid = false;
                    }
                    if (!isValid) {
                        e.preventDefault();
                    }
                });
            }
        }

        $(document).ready(function() {
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('jQuery validate loaded:', typeof $.fn.validate !== 'undefined');
            // Initialize validation when ready
            waitForValidate();

            // Trustpilot OAuth integration - no form validation needed

            // Initialize tab state management
            initializeTabStateManagement();
        });



        function initializeTabStateManagement() {
            // Get URL parameters
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }

            // Update URL with tab parameter
            function updateUrl(tabName) {
                const url = new URL(window.location);
                url.searchParams.set('tab', tabName);
                window.history.replaceState({}, '', url);
            }

            // Get tab name from tab ID
            function getTabName(tabId) {
                const tabMap = {
                    'pills-change-password': 'change-password',
                    'pills-account': 'account',
                    'pills-stripe': 'stripe',
                    'pills-trustpilot': 'trustpilot',
                    'pills-google-calendar': 'google-calendar',
                    'pills-outlook-calendar': 'outlook-calendar'
                };
                return tabMap[tabId] || 'change-password';
            }

            // Get tab ID from tab name
            function getTabId(tabName) {
                const tabMap = {
                    'change-password': 'pills-change-password',
                    'account': 'pills-account',
                    'stripe': 'pills-stripe',
                    'trustpilot': 'pills-trustpilot',
                    'google-calendar': 'pills-google-calendar',
                    'outlook-calendar': 'pills-outlook-calendar'
                };
                return tabMap[tabName] || 'pills-change-password';
            }

            // Check URL parameter and activate corresponding tab
            const activeTab = getUrlParameter('tab');
            if (activeTab) {
                const tabId = getTabId(activeTab);
                const tabButton = $(`button[data-bs-target="#${tabId}"]`);

                if (tabButton.length > 0) {
                    // Remove active class from all tabs
                    $('.nav-link').removeClass('active');
                    $('.tab-pane').removeClass('show active');

                    // Activate the selected tab
                    tabButton.addClass('active');
                    $(`#${tabId}`).addClass('show active');
                }
            } else {
                // Set default tab in URL if no tab parameter exists
                updateUrl('change-password');
            }

            // Listen for tab clicks and update URL
            $('.setting-tabs').on('click', function() {
                const targetId = $(this).attr('data-bs-target').substring(1); // Remove # from target
                const tabName = getTabName(targetId);
                updateUrl(tabName);
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/setting.blade.php ENDPATH**/ ?>