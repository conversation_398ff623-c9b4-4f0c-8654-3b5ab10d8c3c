<?php $__env->startPush('css'); ?>
    <style>
        .pac-container {
            z-index: 999999 !important;
            position: absolute !important;
        }

        .date-item.disabled {
            background-color: #eee;
            color: #999;
            pointer-events: none;
            cursor: not-allowed;
        }

        .date-item {
            border-radius: 6px;
            border: 1px solid #F0F0F0;
            background: #FFF;
            box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
            padding: 12px 24px;
        }

        div#dateGrid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            width: 100%;
        }

        div#timeGrid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .date-item.selected,
        .time-item.selected {
            border-radius: 6px;
            border: 2px solid #020C87 !important;
            background: #FFF;
        }

        .time-item,
        div#selectionDisplay {
            border-radius: 6px;
            border-radius: 6px;
            border: 1px solid #F0F0F0;
            background: #FFF;
            box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
            padding: 8px 3em;
        }

        select#monthSelect {
            border: unset;
            margin-block: 1em;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
        }


        .category-checkbox:hover {
            border-color: #d1d5db;
        }

        .category-checkbox input[type="radio"] {
            appearance: none;
        }

        .category-checkbox {
            border-radius: 6px;
            border: 1px solid #F0F0F0;
            background: #FFF;
            box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
            padding: 12px;
            width: 100%;
        }

        .category-checkbox .span-class-service {
            font-size: 13px;
            color: #36363699;
            background-color: #FFF;
        }

        .category-checkbox p {
            display: flex;
            flex-direction: column;
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .category-checkbox:has(input[type="radio"]:checked),
        .date-item.today {
            border-radius: 6px;
            border: 2px solid #020C87;
            background: #FFF;
            box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05);
            width: 100%;
            padding: 12px;
        }

        .date-item {
            text-align: center;
        }

        .cancel-btn {
            padding: 8px 14px;
            background: transparent;
            color: black;
            font-size: 14px;
            cursor: pointer;
            border-radius: 8px;
            border: 1px solid var(--neutral-gray);
        }

        .save-btn {
            padding: 5px 16px;
            font-family: Inter;
            color: var(--white);
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            border-radius: 8px;
            background: var(--deep-blue);
            border: 1px solid transparent;
        }

        .time-item.disabled {
            opacity: 0.5;
            pointer-events: none;
            background-color: #e0e0e0;
            color: #999;
            cursor: not-allowed;
        }

        /* Professional selection styles */
        .professional-option {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .professional-option:hover {
            transform: translateY(-2px);
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        }

        .professional-option input[type="checkbox"] {
            display: none;
        }

        .professional-option:has(input[type="checkbox"]:checked) {
            border: 2px solid #020C87 !important;
            background: rgba(2, 12, 135, 0.05);
        }

        .professional-option:has(input[type="checkbox"]:checked) .professional-name {
            color: #020C87 !important;
            font-weight: 600;
        }

        #selected-professionals {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        #selected-professionals .badge {
            font-size: 0.875rem;
            padding: 8px 12px;
            display: inline-flex;
            align-items: center;
        }

        #selected-professionals .btn-close {
            font-size: 0.7rem;
            padding: 0;
            margin: 0;
            width: 16px;
            height: 16px;
        }
    </style>
<?php $__env->stopPush(); ?>
<div class="modal fade add-services-modal service-details-modal" id="service-details" tabindex="-1"
    aria-labelledby="serviceDetails" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold" id="filtersModalLabel">Add Service Details</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="service-detail-modal-body">
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="cancel-btn" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="save-btn" id="add-to-cart-service">Add to Cart</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('js'); ?>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA1Qkj7ocEmNs4U5-T2vVJ04OrsCRH0tCU&libraries=places&v=weekly"
        async defer></script>
    <script>
        function updateBookingDateInput(dateObj = null, time = null) {
            const date = new Date(dateObj);
            let formattedDate = date.toLocaleDateString('en-CA'); // "2025-07-23"
            $('input[name="booking_date"]').val(formattedDate); // Set the date input
            $('input[name="booking_time"]').val(time); // Set the date input
            return true;
        }
        window.updateBookingDateInput = updateBookingDateInput;
        $(document).ready(function() {
            // Initialize booking date if there's a pre-selected date
            const preSelectedDate = $('.date-item.selected').data('date');
            if (preSelectedDate) {
                // Format and set the pre-selected date
                const parts = preSelectedDate.split(' ');
                const monthMap = {
                    Jan: '01',
                    Feb: '02',
                    Mar: '03',
                    Apr: '04',
                    May: '05',
                    Jun: '06',
                    Jul: '07',
                    Aug: '08',
                    Sep: '09',
                    Oct: '10',
                    Nov: '11',
                    Dec: '12'
                };
                const day = parts[2].padStart(2, '0');
                const month = monthMap[parts[1]];
                const year = parts[3];
                const formattedDate = `${year}-${month}-${day}`;
            }
            $('input[name="loc"]').on('change', function() {
                var selectedValue = $('input[name="loc"]:checked').val();

                if (selectedValue === "Providers location") {
                    $('#providers-loc').show();
                    $('#home-loc').hide();
                } else if (selectedValue === "Your Home/Office") {
                    $('#providers-loc').hide();
                    $('#home-loc').show();
                }
            });
            $(document).on('click', ".family_category", function(e) {
                let type = $(this).val();
                $("#service_availability").html('');
                if (type == "family") {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo e(url('get-family-details')); ?>/" + type,
                        data: "data",
                        success: function(response) {
                            if (response.status == true) {
                                $("#service_availability").html(response.data);
                            }
                        }
                    });
                }
            });

            // Handle professional checkbox selection
            $(document).on('change', '.professional-checkbox-input', function() {
                updateSelectedProfessionals();
            });

            function updateSelectedProfessionals() {
                let selectedProfessionals = [];
                let selectedContainer = $('#selected-professionals');
                let selectedList = $('#selected-professionals-list');

                // Clear previous selections
                selectedList.empty();

                // Get all checked professionals
                $('input[name="professionals[]"]:checked').each(function() {
                    let professionalName = $(this).closest('.professional-option').data('professional-name');
                    selectedProfessionals.push(professionalName);

                    // Add to selected list
                    selectedList.append(`
                        <span class="badge bg-primary me-2 mb-2">
                            ${professionalName}
                            <button type="button" class="btn-close btn-close-white ms-2"
                                    data-professional-id="${$(this).val()}"
                                    onclick="removeProfessional(this)"></button>
                        </span>
                    `);
                });

                // Show/hide selected professionals section
                if (selectedProfessionals.length > 0) {
                    selectedContainer.show();
                } else {
                    selectedContainer.hide();
                }
            }

            // Function to remove professional
            window.removeProfessional = function(button) {
                let professionalId = $(button).data('professional-id');
                $(`input[name="professionals[]"][value="${professionalId}"]`).prop('checked', false);
                updateSelectedProfessionals();
            };
        });
        // Function to update only time slots for selected date (without reloading modal)
        function updateTimeSlotsForDate(service_id, date) {
            let formattedDate = date ? date : new Date().toISOString().split('T')[0];
            const url = "<?php echo e(url('get-service-time-slots')); ?>/" + service_id + "?date=" + formattedDate;

            $.get(url, function(response) {

                if (response.status) {
                    // Update only the time slots without reloading the entire modal
                    updateTimeSlotDisplay(response.slots, formattedDate, response.bookedSlots);
                } else {
                    console.warn('Failed to fetch time slots:', response.message);
                    // Fallback: clear time slots if none available
                    $('#timeGrid').html('<div class="no-time-slots">No available time slots for this date</div>');
                }
            }).fail(function() {
                console.error('Error fetching time slots');
                $('#timeGrid').html('<div class="no-time-slots">Error loading time slots</div>');
            });
        }
        // Function to update time slot display
        function updateTimeSlotDisplay(slots, date, bookedSlots = []) {
            const timeGrid = document.getElementById("timeGrid");
            if (!timeGrid) return;

            timeGrid.innerHTML = ""; // Clear previous slots

            if (!slots || slots.length === 0) {
                const message = document.createElement("div");
                message.className = "no-time-slots";
                message.textContent = "No available time slots for this date";
                timeGrid.appendChild(message);
                return;
            }

            const today = new Date();
            const selectedDate = new Date(date);
            const isToday = selectedDate.toDateString() === today.toDateString();
            const currentTime = today.getHours() * 60 + today.getMinutes();

            const timeSlots = generateTimeSlots(slots[0], date);

            timeSlots.forEach(function(slot) {
                const timeItem = document.createElement("div");
                timeItem.className = "time-item";
                timeItem.textContent = slot.time;
                timeItem.setAttribute("data-time", slot.time);
                let shouldDisable = slot.disabled;
                // Disable if time is in the past (today)
                if (isToday && !shouldDisable) {
                    const slotTime = parseTimeToMinutes(slot.time);
                    if (slotTime <= currentTime) {
                        shouldDisable = true;
                    }
                }
                // Disable if time is already booked
                if (bookedSlots.includes(slot.time + ':00')) {
                    shouldDisable = true;
                }
                if (shouldDisable) {
                    timeItem.classList.add("disabled");
                } else {
                    timeItem.addEventListener("click", function() {
                        document.querySelectorAll(".time-item").forEach(t => t.classList.remove(
                            "selected"));
                        this.classList.add("selected");

                        const selectedDate = $('#dateGrid .date-item.selected').data('date');
                        const selectedTime = this.getAttribute('data-time');
                        if (selectedDate) {
                            updateBookingDateInput(new Date(selectedDate), selectedTime);
                        }
                    });
                }
                timeGrid.appendChild(timeItem);
            });
        }
        // Helper function to convert time string to minutes
        function parseTimeToMinutes(timeString) {
            const timeParts = timeString.match(/(\d+):(\d+)\s*(AM|PM)/i);
            if (!timeParts) return 0;

            let hours = parseInt(timeParts[1]);
            const minutes = parseInt(timeParts[2]);
            const period = timeParts[3].toUpperCase();

            // Convert to 24-hour format
            if (period === 'PM' && hours !== 12) {
                hours += 12;
            } else if (period === 'AM' && hours === 12) {
                hours = 0;
            }

            return hours * 60 + minutes;
        }

        function ajaxCall(service_id, date = null) {
            let formattedDate = date ? new Date(date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
            const url = "<?php echo e(url('get-service-details')); ?>/" + service_id + (formattedDate ? "?date=" + formattedDate : '');
            $.get(url, function(response) {
                if (response.status) {
                    $('#service-details').modal('show');
                    $("#service-detail-modal-body").html(response.data);

                    // Check if editing and use cart date for calendar initialization
                    let calendarDate = formattedDate;
                    if (window.cartData && window.cartData.isEdit && window.cartData.booking_date) {
                        calendarDate = window.cartData.booking_date;
                    }

                    initializeCalendar(response.services, calendarDate, response.slots);

                    // Pre-populate fields if editing existing cart item
                    if (window.cartData && window.cartData.isEdit) {
                        populateCartData(window.cartData);
                    }
                    // Disable location inputs if requested and location type is provider
                    if (window.shouldDisableLocationInputs) {
                        let loc = $('input[name="loc"]:checked').val();
                        if (loc === "provider") {
                            $('#pac-input').prop('disabled', true);
                        }
                        window.shouldDisableLocationInputs = false; // Reset flag
                    }
                    // Initialize Google Maps after modal content is loaded
                    setTimeout(function() {
                        initServiceMap();
                    }, 100);
                } else {
                    Swal.fire({
                        text: response.message,
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Ok, got it!",
                        customClass: {
                            confirmButton: "btn btn-primary"
                        }
                    });
                }
            });
        }
        $(document).on("click", "#add-to-cart-service", function() {
            let booking_date = $('input[name="booking_date"]').val();
            let booking_time = $('input[name="booking_time"]').val();
            let service_id = $('input[name="service_id"]').val();
            let comments = $('textarea[name="comments"]').val();
            let loc = $('input[name="loc"]:checked').val();
            let lat = $('input[name="lat"]').val();
            let lng = $('input[name="lng"]').val();
            let address = $('#pac-input').val();

            // Get selected professionals
            let selectedProfessionals = [];
            $('input[name="professionals[]"]:checked').each(function() {
                selectedProfessionals.push({
                    id: $(this).val(),
                    name: $(this).closest('.professional-option').data('professional-name')
                });
            });

            // Validation: Check if professionals are selected for business services
            let isBusinessService = $('#professional-selection').length > 0;
            if (isBusinessService && selectedProfessionals.length === 0) {
                return;
            }
            if (booking_date && booking_time) {
                $.ajax({
                    url: '<?php echo e(route('saveBookingDetails')); ?>',
                    type: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        booking_date: booking_date,
                        booking_time: booking_time,
                        service_id: service_id,
                        comments: comments,
                        loc: loc,
                        lat: lat,
                        lng: lng,
                        address: address,
                        selected_professionals: selectedProfessionals
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#service-details').modal('hide');
                            swal.fire({
                                text: "Booking details added successfully",
                                icon: "success",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary"
                                }
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.href = '/customer/cart';
                                }
                            });
                        } else {
                            alert('Error saving booking details');
                        }
                    },
                    error: function() {
                        alert('An error occurred while saving booking details');
                    }
                });
            } else {
                alert('Please select both date and time');
            }
        });
        $(document).on("click", ".add-to-cart-btn", function() {
            let service_id = $(this).data('id');
            let isEdit = $(this).data('is-edit') || false;

            // Store cart data for pre-population if editing
            if (isEdit) {
                let selectedProfessionalsData = $(this).data('selected-professionals');
                let selectedProfessionals = [];

                // Parse selected professionals data
                if (selectedProfessionalsData) {
                    try {
                        if (typeof selectedProfessionalsData === 'string') {
                            selectedProfessionals = JSON.parse(selectedProfessionalsData);
                        } else {
                            selectedProfessionals = selectedProfessionalsData;
                        }
                    } catch (e) {
                        console.warn('Error parsing selected professionals data:', e);
                        selectedProfessionals = [];
                    }
                }

                window.cartData = {
                    booking_date: $(this).data('booking-date'),
                    booking_time: $(this).data('booking-time'),
                    location_type: $(this).data('location-type'),
                    comments: $(this).data('comments'),
                    address: $(this).data('address'),
                    lat: $(this).data('lat'),
                    lng: $(this).data('lng'),
                    selected_professionals: selectedProfessionals,
                    isEdit: true
                };

                console.log('Cart data for editing:', window.cartData);
            } else {
                window.cartData = {
                    isEdit: false
                };
            }

            // Store the disabled state to apply after modal loads
            window.shouldDisableLocationInputs = true;
            ajaxCall(service_id);
        });
        $(document).on("click", "#dateGrid .date-item", function() {
            $(".date-item").removeClass("selected");
            $(this).addClass("selected");
            let service_id = $('input[name="service_id"]').val();
            var initialSelectedDate = $('#dateGrid .date-item.selected').data('date');
            let formattedDate = new Date(initialSelectedDate);
            let selectedTime = $('#timeGrid .time-item.selected').data('time');
            updateBookingDateInput(formattedDate, selectedTime);
            var newformattedDate = null;
            if (initialSelectedDate) {
                // Parse the date string properly to avoid timezone issues
                var parts = initialSelectedDate.split(' ');
                var monthMap = {
                    Jan: '01',
                    Feb: '02',
                    Mar: '03',
                    Apr: '04',
                    May: '05',
                    Jun: '06',
                    Jul: '07',
                    Aug: '08',
                    Sep: '09',
                    Oct: '10',
                    Nov: '11',
                    Dec: '12'
                };
                var day = parts[2].padStart(2, '0');
                var month = monthMap[parts[1]];
                var year = parts[3];
                newformattedDate = `${year}-${month}-${day}`;
            }
            // Just update time slots for the selected date, don't reload entire modal
            updateTimeSlotsForDate(service_id, newformattedDate);
        });
        $(document).on("click", "#timeGrid .time-item", function() {
            let selectedTime = $(this).data('time'); // Get the selected time from the data-time attribute
            let initialDateStr = $('#dateGrid .date-item.selected').data('date'); // Get string
            let initialDate = new Date(initialDateStr); // Convert to Date object
            if (!isNaN(initialDate)) {
                updateBookingDateInput(initialDate, selectedTime); // Call your custom function
            } else {
                console.error('Invalid date selected');
            }
        });

        function generateTimeSlots(slot, date) {
            const timeSlots = [];
            const pad = num => num.toString().padStart(2, '0');

            // Parse slot start and end times
            let [startHour, startMin] = slot.start_time.split(':').map(Number);
            let [endHour, endMin] = slot.end_time.split(':').map(Number);
            const duration = parseInt(slot.duration); // Duration in minutes

            const now = new Date(); // Current datetime
            const selectedDate = new Date(date); // selected date passed as argument

            const start = new Date(selectedDate);
            start.setHours(startHour, startMin, 0, 0);

            const end = new Date(selectedDate);
            end.setHours(endHour, endMin, 0, 0);

            while (start < end) {
                const currentSlot = new Date(start); // clone current time
                const timeLabel = `${pad(currentSlot.getHours())}:${pad(currentSlot.getMinutes())}`;
                let isDisabled = false;

                // 1. Disable past slots only if the selected date is today
                const isToday =
                    now.toDateString() === selectedDate.toDateString();
                if (isToday && currentSlot < now) {
                    isDisabled = true;
                }
                // 2. Disable lunch break (e.g., between 13:00 and 14:00)
                // if (currentSlot.getHours() === 13) {
                //     isDisabled = true;
                // }
                // // 3. Disable specific blocked times
                // const blockedTimes = ["11:30", "15:00"];
                // if (blockedTimes.includes(timeLabel)) {
                //     isDisabled = true;
                // }
                // // 4. Disable weekends
                // const day = currentSlot.getDay();
                // if (day === 0 || day === 6) {
                //     isDisabled = true;
                // }
                // Add slot to array
                timeSlots.push({
                    time: timeLabel,
                    disabled: isDisabled
                });
                // Move to next slot
                start.setMinutes(start.getMinutes() + duration);
            }
            return timeSlots;
        }
        function initializeCalendar(services = null, mydate = null, slots = null) {
            let currentWeekStart = mydate ? new Date(mydate) : new Date();
            let selectedDate = mydate ? new Date(mydate) : null; // Don't default to today, let user select
            let selectedTime = null;
            let formatedDate = selectedDate.toISOString().split('T')[0];
            const timeSlots = slots[0] ? generateTimeSlots(slots[0], formatedDate) : [];
            const monthNames = [
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            ];
            const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
            const today = new Date();

            function setToStartOfWeek(date) {
                const dayOfWeek = date.getDay();
                date.setDate(date.getDate() - dayOfWeek);
                return date;
            }

            function generateMonthOptions() {
                const monthSelect = document.getElementById("monthSelect");
                if (!monthSelect) return;
                monthSelect.innerHTML = "";
                for (let i = 0; i < 12; i++) {
                    const date = new Date();
                    date.setMonth(date.getMonth() + i);

                    const option = document.createElement("option");
                    option.value = date.getFullYear() + "-" + date.getMonth();
                    option.textContent = monthNames[date.getMonth()] + " " + date.getFullYear();

                    if (i === 0) {
                        option.selected = true;
                    }

                    monthSelect.appendChild(option);
                }
            }

            function renderDates(disabledDates = []) {
                // Get the container where date items will be rendered
                const dateGrid = document.getElementById("dateGrid");
                if (!dateGrid) return;
                // Clear any previously rendered dates
                dateGrid.innerHTML = "";
                // Loop to render 7 days (a full week)
                for (let i = 0; i < 7; i++) {
                    // Create a date starting from currentWeekStart and add 'i' days
                    const date = new Date(currentWeekStart);
                    date.setDate(currentWeekStart.getDate() + i);
                    // Format date as "YYYY-MM-DD" for comparison
                    const formattedDate = date.toISOString().split("T")[0];
                    // Create a new date item element
                    const dateItem = document.createElement("div");
                    dateItem.className = "date-item"; // Add class for styling and selector
                    dateItem.setAttribute("data-date", date.toDateString()); // Store readable date for later use
                    // --- Check if the date is in the disabledDates list ---
                    const isInDisabledList = disabledDates.includes(formattedDate);
                    // --- Check if the date is in the past ---
                    const now = new Date();
                    now.setHours(0, 0, 0, 0); // Remove time portion to compare only the date
                    const isPastDate = date < now;
                    // --- Highlight the selected date if it matches ---
                    if (selectedDate && isSameDay(date, selectedDate)) {
                        dateItem.classList.add("selected");
                    }
                    // --- If date is disabled or in the past, mark as disabled ---
                    if (isInDisabledList || isPastDate) {
                        dateItem.classList.add("disabled");
                    } else {
                        // If date is valid, make it clickable and bind the click handler
                        dateItem.addEventListener("click", function() {

                            selectDate(this, date); // Pass the clicked element and date to the selectDate function
                        });
                    }
                    // --- Create inner HTML showing date number and day name ---
                    dateItem.innerHTML =
                        '<div class="date-number">' + date.getDate() + '</div>' + // e.g. 17
                        '<div class="date-day">' + dayNames[date.getDay()] + '</div>'; // e.g. Wed

                    // Append the date item to the grid
                    dateGrid.appendChild(dateItem);
                }
                // Update the dropdown to reflect the current visible week’s month
                updateMonthSelect();
            }

            function renderTimeSlots() {
                const timeGrid = document.getElementById("timeGrid");
                if (!timeGrid) return;

                timeGrid.innerHTML = ""; // Clear previous items

                if (timeSlots.length === 0) {
                    // If no slots available
                    const message = document.createElement("div");
                    message.className = "no-time-slots";
                    message.textContent = "No available time slots";
                    timeGrid.appendChild(message);
                } else {
                    // If slots are available
                    timeSlots.forEach(function(slot) {
                        const timeItem = document.createElement("div");
                        timeItem.className = "time-item";
                        timeItem.textContent = slot.time;
                        timeItem.setAttribute("data-time", slot.time);

                        // Add "disabled" class and prevent selection if disabled
                        if (slot.disabled) {
                            timeItem.classList.add("disabled");
                        } else {
                            // If enabled, allow click
                            timeItem.addEventListener("click", function() {
                                selectTime(this, slot.time);
                            });

                            if (selectedTime === slot.time) {
                                timeItem.classList.add("selected");
                            }
                        }

                        timeGrid.appendChild(timeItem);
                    });
                }
            }

            function selectDate(element, date) {
                const allDates = document.querySelectorAll(".date-item");
                allDates.forEach(d => d.classList.remove("selected"));

                element.classList.add("selected");
                selectedDate = new Date(date);
                updateDisplay();
                console.log('Selected date:', $(element).data('date'));
            }

            function selectTime(element, time) {
                const allTimes = document.querySelectorAll(".time-item");
                allTimes.forEach(t => t.classList.remove("selected"));

                element.classList.add("selected");
                selectedTime = time;
                updateDisplay();
            }

            function updateDisplay() {
                const display = document.getElementById("selectionDisplay");
                if (!display) return;
                if (selectedDate && selectedTime) {
                    const options = {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    };
                    const dateStr = selectedDate.toLocaleDateString('en-US', options);
                    display.innerHTML = '<strong>Selected:</strong> ' + dateStr + ' at ' + selectedTime;
                } else if (selectedDate) {
                    const options = {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    };
                    const dateStr = selectedDate.toLocaleDateString('en-US', options);
                    display.innerHTML = '<strong>Selected:</strong> ' + dateStr + ' - Please select a time';
                } else {
                    display.innerHTML = '<div class="no-selection">Select a date and time</div>';
                }
            }

            function changeWeek(direction) {
                currentWeekStart.setDate(currentWeekStart.getDate() + (direction * 7));
                renderDates(["2025-07-13", "2025-07-14"]);
            }

            function updateMonthSelect() {
                const monthSelect = document.getElementById("monthSelect");
                if (!monthSelect) return;

                const middleDate = new Date(currentWeekStart);
                middleDate.setDate(middleDate.getDate() + 3);

                const targetValue = middleDate.getFullYear() + "-" + middleDate.getMonth();

                for (let i = 0; i < monthSelect.options.length; i++) {
                    if (monthSelect.options[i].value === targetValue) {
                        monthSelect.selectedIndex = i;
                        break;
                    }
                }
            }

            function setupEventListeners() {
                const prevBtn = document.getElementById("prevBtn");
                const nextBtn = document.getElementById("nextBtn");
                const monthSelect = document.getElementById("monthSelect");
                if (prevBtn) {
                    prevBtn.addEventListener("click", function() {
                        changeWeek(-1);
                    });
                }
                if (nextBtn) {
                    nextBtn.addEventListener("click", function() {
                        changeWeek(1);
                    });
                }
                if (monthSelect) {
                    monthSelect.addEventListener("change", function() {
                        const value = this.value.split("-");
                        const year = parseInt(value[0]);
                        const month = parseInt(value[1]);

                        const newDate = new Date(year, month, 1);
                        currentWeekStart = setToStartOfWeek(newDate);
                        renderDates(["2025-07-13", "2025-07-14"]);
                    });
                }
            }

            function isSameDay(date1, date2) {
                return date1.getDate() === date2.getDate() &&
                    date1.getMonth() === date2.getMonth() &&
                    date1.getFullYear() === date2.getFullYear();
            }

            // Initialize calendar
            currentWeekStart = setToStartOfWeek(currentWeekStart);
            generateMonthOptions();
            renderDates(["2025-07-13", "2025-07-14"]);
            renderTimeSlots();
            setupEventListeners();
            updateDisplay();
            updateBookingDateInput(formatedDate, selectedTime);
        }
        $(document).on('change', 'input[name="loc"]', function() {
            var selectedValue = $('input[name="loc"]:checked').val();
            if (selectedValue === "provider") {
                $('#providers-loc').show();
                $('#home-loc').hide();
                $('#pac-input').prop('disabled', true);
                // Update coordinates to service location
                updateMapToServiceLocation();
            } else if (selectedValue === "home") {
                $('#providers-loc').hide();
                $('#home-loc').show();
                $('#pac-input').prop('disabled', false);
                // Set coordinates to user's profile location for home
                const userLat = '<?php echo e(auth()->user()->profile?->lat ?? ''); ?>';
                const userLng = '<?php echo e(auth()->user()->profile?->lng ?? ''); ?>';

                if (userLat && userLng) {
                    $('#latitude').val(userLat);
                    $('#longitude').val(userLng);

                    // Get address from user's coordinates
                    getAddressFromCoordinates(userLat, userLng, function(address) {
                        if (address) {
                            $('#pac-input').val(address);
                        }
                    });
                } else {
                    // If no user profile coordinates, clear fields
                    $('#latitude').val('');
                    $('#longitude').val('');
                    $('#pac-input').val('');
                }

                // Reinitialize map for home location
                setTimeout(function() {
                    initServiceMap();
                }, 100);
            } else {
                $('#pac-input').prop('disabled', false);
            }
        });
        // Function to update cart display after deletion
        function updateCartDisplay() {
            const cartCount = $('.cart-item').length;
            $('.cart-count').text(cartCount);

            // Update any cart badges or counters
            if (cartCount === 0) {
                $('.cart-badge').hide();
            }

            // Recalculate and update total if total element exists
            updateCartTotal();
        }
        // Function to recalculate cart total
        function updateCartTotal() {
            let total = 0;
            $('.cart-item').each(function() {
                const priceText = $(this).find('.item-price').text().replace('$', '');
                const price = parseFloat(priceText) || 0;
                total += price;
            });

            // Update total display
            $('.cart-total').text('$' + total.toFixed(2));
        }
        // Function to populate cart data when editing
        function populateCartData(cartData) {
            if (cartData.booking_date) {
                $('input[name="booking_date"]').val(cartData.booking_date);

                // No need to select date again since calendar is already initialized with correct date
                // The date should already be selected from initializeCalendar
            }
            if (cartData.booking_time) {
                $('input[name="booking_time"]').val(cartData.booking_time);
                setTimeout(function() {
                    selectTimeSlot(cartData.booking_time);
                }, 500);
            }
            if (cartData.location_type) {
                $('input[name="loc"][value="' + cartData.location_type + '"]').prop('checked', true);
                $('input[name="loc"][value="' + cartData.location_type + '"]').trigger('change');
            }
            if (cartData.comments) {
                $('textarea[name="comments"]').val(cartData.comments);
            }
            if (cartData.address) {
                $('#pac-input').val(cartData.address);
            }
            if (cartData.lat) {
                $('#latitude').val(cartData.lat);
            }
            if (cartData.lng) {
                $('#longitude').val(cartData.lng);
            }

            // Pre-select professionals if editing
            if (cartData.selected_professionals && cartData.selected_professionals.length > 0) {
                setTimeout(function() {
                    selectProfessionals(cartData.selected_professionals);
                }, 800); // Wait for modal content to fully load
            }
        }

        // Function to select professionals visually
        function selectProfessionals(selectedProfessionals) {
            console.log('Selecting professionals:', selectedProfessionals);

            // Clear all previous selections
            $('input[name="professionals[]"]').prop('checked', false);
            $('.professional-option').removeClass('selected');

            // Select the professionals from cart data
            selectedProfessionals.forEach(function(professional) {
                let professionalId = professional.id;
                let checkbox = $('input[name="professionals[]"][value="' + professionalId + '"]');

                if (checkbox.length > 0) {
                    checkbox.prop('checked', true);
                    checkbox.closest('.professional-option').addClass('selected');
                    console.log('Selected professional:', professional.name, 'ID:', professionalId);
                } else {
                    console.warn('Professional checkbox not found for ID:', professionalId);
                }
            });

            // Update the selected professionals display
            updateSelectedProfessionals();
        }
        // Function to select time slot visually
        function selectTimeSlot(timeValue) {
            // Remove selection from all time items
            const allTimes = document.querySelectorAll(".time-item");
            allTimes.forEach(t => t.classList.remove("selected"));

            // Find and select the matching time slot
            const timeItem = document.querySelector('.time-item[data-time="' + timeValue + '"]');
            if (timeItem) {
                timeItem.classList.add("selected");
                console.log('Time slot selected:', timeValue);
            } else {
                console.warn('Time slot not found:', timeValue);
            }
        }
        // Function to select date item visually
        function selectDateItem(dateValue) {
            // Remove selection from all date items
            const allDates = document.querySelectorAll(".date-item");
            allDates.forEach(d => d.classList.remove("selected"));

            // Find and select the matching date item
            // The dateValue might be in format "2024-01-15", but data-date might be in different format
            // Try to find by data-date attribute
            const dateItem = document.querySelector('.date-item[data-date*="' + dateValue + '"]');
            if (dateItem) {
                dateItem.classList.add("selected");
                console.log('Date item selected:', dateValue);
            } else {
                // Try alternative approach - convert date format and search
                try {
                    const date = new Date(dateValue);
                    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    const formattedDate = months[date.getMonth()] + ' ' + date.getDate() + ' ' + date.getFullYear();

                    const alternativeDateItem = document.querySelector('.date-item[data-date="' + formattedDate + '"]');
                    if (alternativeDateItem) {
                        alternativeDateItem.classList.add("selected");
                        console.log('Date item selected (alternative format):', formattedDate);
                    } else {
                        console.warn('Date item not found:', dateValue);
                    }
                } catch (e) {
                    console.warn('Date item not found and format conversion failed:', dateValue);
                }
            }
        }
        function updateMapToServiceLocation()
        {
            const $latElement = $('#latitude');
            const $lngElement = $('#longitude');
            // Get service coordinates from data attributes
            const serviceLat = parseFloat($latElement.data('service-lat'));
            const serviceLng = parseFloat($lngElement.data('service-lng'));
            if (serviceLat && serviceLng && serviceLat !== 0 && serviceLng !== 0) {
                $latElement.val(serviceLat);
                $lngElement.val(serviceLng);

                setTimeout(function() {
                    initServiceMap();
                }, 100);
            } else {
                console.warn('Service coordinates not available');
                setTimeout(function() {
                    initServiceMap();
                }, 100);
            }
        }
        function initServiceMap() {
            const $mapElement = $('#map');
            const $inputElement = $('#pac-input');
            const $latElement = $('#latitude');
            const $lngElement = $('#longitude');
            if (!$mapElement.length || !$inputElement.length || !$latElement.length || !$lngElement.length) {
                console.warn('Service Map: Required elements not found');
                return;
            }
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                console.warn('Google Maps API not loaded yet, retrying...');
                setTimeout(initServiceMap, 500);
                return;
            }

            let lat, lng;
            const locType = $('input[name="loc"]:checked').val();

            if (locType === "provider") {
                const serviceLat = parseFloat($latElement.val());
                const serviceLng = parseFloat($lngElement.val());

                if (serviceLat && serviceLng && serviceLat !== 0 && serviceLng !== 0) {
                    lat = serviceLat;
                    lng = serviceLng;
                } else {
                    lat = -33.8688;
                    lng = 151.2195;
                }
            } else {
                // For home location, use user's profile coordinates if available
                const userLat = parseFloat($latElement.val());
                const userLng = parseFloat($lngElement.val());

                if (userLat && userLng && userLat !== 0 && userLng !== 0) {
                    lat = userLat;
                    lng = userLng;
                } else {
                    // Fallback to default coordinates if no user profile coordinates
                    lat = -33.8688;
                    lng = 151.2195;
                }
            }
            const defaultLatLng = {
                lat: lat,
                lng: lng
            };
            const input = document.getElementById("pac-input");
            const map = new google.maps.Map($mapElement[0], {
                center: defaultLatLng,
                zoom: 14,
                mapTypeControl: false,
                streetViewControl: false,
                rotateControl: true,
            });

            const searchBox = new google.maps.places.SearchBox(input);
            const geocoder = new google.maps.Geocoder();

            const marker = new google.maps.Marker({
                map,
                position: defaultLatLng,
                draggable: true,
            });

            function updateLocationFields(lat, lng) {
                $latElement.val(lat);
                $lngElement.val(lng);
            }

            function updateAddressFromLatLng(lat, lng) {
                geocoder.geocode({
                    location: {
                        lat,
                        lng
                    }
                }, (results, status) => {
                    if (status === "OK" && results[0]) {
                        $inputElement.val(results[0].formatted_address);
                    } else {
                        $inputElement.val("");
                        console.warn("Geocoder failed due to: " + status);
                    }
                });
            }
            // Update location fields and address based on location type
            if (locType === "provider") {
                updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
            } else {
                // For home location, update both fields and address with user's profile coordinates
                updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
            }
            google.maps.event.addListener(marker, "dragend", function(event) {
                const lat = event.latLng.lat();
                const lng = event.latLng.lng();
                updateLocationFields(lat, lng);
                updateAddressFromLatLng(lat, lng);
            });

            searchBox.addListener("places_changed", () => {
                const places = searchBox.getPlaces();
                if (!places.length || !places[0].geometry?.location) return;

                const location = places[0].geometry.location;
                marker.setPosition(location);
                map.setCenter(location);
                map.setZoom(18);

                const lat = location.lat();
                const lng = location.lng();
                updateLocationFields(lat, lng);
                $inputElement.val(places[0].formatted_address || "");
            });
            console.log('Service Map: Initialization completed successfully');
        }
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/templates/modal/add-service-details-modal.blade.php ENDPATH**/ ?>