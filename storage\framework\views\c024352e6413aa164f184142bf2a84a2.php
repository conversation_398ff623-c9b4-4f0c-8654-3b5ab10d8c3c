<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12">
                    <h4 class="sora black">My Bookings</h4>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
            <div id="kt_app_content_container" class="app-container container-fluid padding-block booking-section ">
                <div class="row">
                    <div class="col-lg-3  col-md-4 calender-box">
                        <div class="d-flex align-items-center  justify-content-end">
                            <div id="inline-calendar"></div>
                        </div>
                        <!-- Buttons below the calendar -->
                        <div class="mt-2 d-flex justify-content-end  gap-3">
                            <button id="cancel-btn" class="btn btn-secondary btn-sm">Cancel</button>
                            <button id="apply-btn" class=" button blue-button px-5 py-2">Apply</button>
                        </div>
                    </div>
                    <div class="col-lg-9 col-md-10 p-0">
                        <div class="schedule-container customer-calender ">
                            <div class="calendar-header flex-align-space-btw">
                                <div class="flex-align-space-btw">
                                    <div class="calendar-controls d-flex  gap-2 align-items-center">
                                        <div class="d-flex gap-4">
                                            <p id="today" class="m-0 fs-13 regular black">Today</p>
                                            <button id="prev-week" class="btn-prev"><i
                                                    class="fa-solid fa-chevron-left"></i></button>
                                            <button id="next-week" class="btn-next"><i
                                                    class="fa-solid fa-chevron-right"></i></button>
                                        </div>
                                        <h3 id="date-range" class="m-0 fs-16 semi-bold black">14 July 2025 - 20 July 2025
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div id="calendar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
            <div id="kt_app_content_container" class="app-container container padding-block">

                <div class="row row-gap-5">
                    <!-- <div class="col-md-12">
                                                    <h4 class="sora black">My Bookings</h4>
                                                </div> -->


                    <div class="col-md-12">
                        <div class="card-box">
                            <p class="fs-18 semi_bold sora">Upcoming Bookings</p>
                            <div class="row">
                                <?php $__currentLoopData = $upcommingBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $upcommingBooking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-12 gap-2">
                                        <div class="card shadow-none booking_card flex-row py-2 px-3 gap-3">
                                            <div
                                                class="card-header p-0 d-flex justify-content-between align-items-center border-0">
                                                <div
                                                    class="d-flex align-items-center flex-column calendar-icon fs-20 semi_bold black">
                                                    <p class="m-0 fs-20 semi_bold sora">
                                                        <?php echo e(Carbon\Carbon::parse($upcommingBooking->booking_date)->format('d')); ?>

                                                    </p>
                                                    <p class="m-0 fs-15 mormal">
                                                        <?php echo e(Carbon\Carbon::parse($upcommingBooking->booking_date)->format('M')); ?>

                                                    </p>

                                                </div>
                                            </div>
                                            <!-- Card Body -->
                                            <div class="card-body  p-0 d-flex justify-content-between align-items-center">
                                                <div>
                                                    <p class="mb-0 fs-15 sora balck normal">
                                                        <?php echo e($upcommingBooking->service->name); ?></p>
                                                    <p class="mb-0 fs-12 sora black normal light-black ">
                                                        
                                                        <?php echo e(Carbon\Carbon::parse($upcommingBooking->booking_date)->format('l, d M Y')); ?>

                                                        
                                                        <span class="status booked">Booked</span>
                                                    </p>
                                                    <p class="mb-0 fs-12 sora black normal light-black opacity-6">Justin
                                                        Dokidis
                                                        with Gustavo Dias</p>
                                                </div>
                                                <div class="d-flex gap-5 align-items-center">
                                                    <p class="fs-20 semi_bold black sora m-0">
                                                        $<?php echo e($upcommingBooking->total_amount); ?></p>
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button"
                                                            id="dropdownMenuButtonBooking" data-bs-toggle="dropdown"
                                                            aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu"
                                                            aria-labelledby="dropdownMenuButtonBooking">
                                                            <li>
                                                                <a href=""
                                                                    data-bs-target="#rescheduleBookingModalLabel"
                                                                    data-bs-toggle="modal"
                                                                    class="dropdown-item  black fs-14 regular "
                                                                    type="button">
                                                                    <i class="bi bi-calendar-event me-2"></i>
                                                                    Reschedule
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="" data-bs-target="#cancelBookingModalLabel"
                                                                    data-bs-toggle="modal"
                                                                    class="dropdown-item cancel fs-14 regular"
                                                                    type="button">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                    Cancel
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                            </div>
                        </div>

                    </div>

                    <div class="col-lg-12">
                        <p class="fs-16 sora black semi_bold">Past Bookings</p>
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <div class="search_box">
                                    <label for="customSearchInput">
                                        <i class="fas fa-search"></i>
                                    </label>
                                    <input class="search_input search" type="text" id="customSearchInput"
                                        placeholder="Search..." />
                                </div>
                                <!-- Select with dots -->
                                <div class="dropdown search_box select-box">
                                    <button
                                        class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span><span class="dot"></span>
                                            All</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                data-color="#4B5563"><span class="dot all"></span>
                                                All</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                data-color="#F59E0B"><span class="dot ongoing"></span>
                                                Ongoing</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                data-color="#3B82F6"><span class="dot upcoming"></span>
                                                Upcoming</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                data-color="#10B981"><span class="dot completed"></span>
                                                Complete</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                Canceled</a></li>
                                    </ul>
                                </div>
                                <!-- Date Picker -->
                                <label for="datePicker" class="date_picker">
                                    <div class="date-picker-container">
                                        <i class="bi bi-calendar-event calender-icon"></i>
                                        <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                        <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                    </div>
                                </label>

                            </div>

                            <!-- 📝 List View Content -->
                            <table id="responsiveTable" class="responsiveTable display nowrap w-100">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Professional</th>
                                        <th>Service Name</th>
                                        <th>Status</th>
                                        <th>Date & Time</th>
                                        <th>Amount</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $pastBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pastBooking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td data-label="Booking ID"><?php echo e($pastBooking->booking_number); ?></td>
                                            <td data-label="Professional"><?php echo e($pastBooking->provider->name ?? "-"); ?></td>
                                            <td data-label="Service Name"><?php echo e($pastBooking->service->name); ?></td>
                                            <td data-label="Status" class="status <?php echo e($pastBooking->status == 1 ? 'paid-status' : 'unpaid-status'); ?>">
                                                <?php echo e($pastBooking->status == 1 ? 'Completed' : 'Cancelled'); ?>

                                            </td>
                                            <td data-label="Date & Time"> <?php echo e(Carbon\Carbon::parse($pastBooking->booking_date)->format("l, d M Y")); ?> - <?php echo e(Carbon\Carbon::parse($pastBooking->booking_time)->format("h:i A")); ?></td>
                                            <td data-label="Amount">$<?php echo e($pastBooking->total_amount); ?></td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButtonTable"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtontable">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular "
                                                                type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular"
                                                                type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No past bookings found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <?php echo $__env->make('dashboard.templates.modal.cancel-booking-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.templates.modal.rescheduled-booking-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->stopSection(); ?>
    <?php $__env->startPush('css'); ?>
        <!--begin::Vendor Stylesheets(used for this page only)-->
        <link href="<?php echo e(asset('website')); ?>/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet"
            type="text/css" />
        <!--end::Vendor Stylesheets-->
        <style>
            /* Custom FullCalendar styles for customer booking */
            .customer-calender #calendar {
                overflow: visible;
            }

            .customer-calender .fc {
                font-family: 'Sora', sans-serif;
            }

            .customer-calender .fc-timegrid-slot {
                height: 40px;
            }

            .customer-calender .fc-timegrid-axis {
                width: 60px;
            }

            .customer-calender .fc-col-header-cell {
                background-color: var(--whisper-gray);
                border: 1px solid var(--border-color);
                padding: 10px;
                font-weight: 600;
                color: var(--black);
            }

            .customer-calender .fc-timegrid-slot {
                border-color: var(--border-color);
            }

            .customer-calender .fc-event {
                border-radius: 6px;
                border: none;
                font-size: 12px;
                font-weight: 500;
                padding: 2px 6px;
            }

            .customer-calender .fc-event-title {
                font-weight: 600;
            }

            .customer-calender .fc-event:hover {
                opacity: 0.8;
                cursor: pointer;
            }

            .customer-calender .fc-timegrid-now-indicator-line {
                border-color: var(--deep-blue);
                border-width: 2px;
            }

            .customer-calender .fc-timegrid-now-indicator-arrow {
                border-left-color: var(--deep-blue);
                border-right-color: var(--deep-blue);
            }

            /* Hide default scrollbars and adjust layout */
            .customer-calender .fc-scroller {
                overflow-x: auto;
                overflow-y: auto;
            }

            .customer-calender .fc-timegrid-body {
                min-width: 100%;
            }

            /* Custom SweetAlert styles for booking details */
            .booking-details-popup {
                font-family: 'Sora', sans-serif !important;
            }

            .booking-details-modal {
                text-align: left;
                padding: 10px;
            }

            .booking-details-modal .row {
                margin-bottom: 10px;
            }

            .booking-details-modal .badge {
                font-size: 12px;
                padding: 5px 10px;
                border-radius: 15px;
            }

            .booking-details-modal .bg-primary {
                background-color: var(--deep-blue) !important;
            }

            .booking-details-modal .bg-secondary {
                background-color: #6c757d !important;
            }

            .booking-details-modal .text-primary {
                color: var(--deep-blue) !important;
            }

            .booking-details-modal .text-success {
                color: #28a745 !important;
            }

            .booking-details-modal .text-muted {
                color: #6c757d !important;
            }

            .booking-details-modal .fw-bold {
                font-weight: 600 !important;
            }
        </style>
    <?php $__env->stopPush(); ?>
    <?php $__env->startPush('js'); ?>
        <!--begin::Vendor Javascripts(used for this page only)-->
        <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>
        <!--end::Vendor Javascripts-->

        <script>
            $(document).ready(function() {
                console.log('Document ready, initializing booking page...');

                // Initialize DataTable for booking page specifically
                if ($("#responsiveTable").length) {
                    try {
                        console.log('Initializing DataTable for booking page...');
                        $("#responsiveTable").DataTable({
                            dom: "rtip",
                            responsive: true,
                            scrollX: false,
                            paging: true,
                            pageLength: 8,
                            lengthChange: false,
                            initComplete: function () {
                                console.log('DataTable initialized successfully');
                                // Set data-label attributes from header text
                                this.api()
                                    .columns()
                                    .header()
                                    .each(function (header) {
                                        var title = $(header).text();
                                        $(header).attr("data-label", title);
                                    });
                            },
                        });
                    } catch (error) {
                        console.error('Booking page DataTable initialization error:', error);
                    }
                }

                console.log('Initializing calendar...');

                // Check if FullCalendar is available
                if (typeof FullCalendar === 'undefined') {
                    console.error('FullCalendar is not loaded!');
                    return;
                }

                var calendarEl = $('#calendar')[0];
                if (!calendarEl) {
                    console.error('Calendar element not found!');
                    return;
                }

                console.log('Calendar element found:', calendarEl);
                var calendar;

                try {
                    // Initialize FullCalendar
                    calendar = new FullCalendar.Calendar(calendarEl, {
                    headerToolbar: false, // We'll use custom header
                    initialView: 'timeGridWeek',
                    height: 'auto',
                    slotMinTime: '08:00:00',
                    slotMaxTime: '20:00:00',
                    slotDuration: '00:30:00',
                    allDaySlot: false,
                    nowIndicator: true,
                    editable: false,
                    selectable: false,
                    selectMirror: false,
                    dayMaxEvents: true,
                    weekends: true,
                    events: {
                        url: '<?php echo e(route('customer_booking_calendar_data')); ?>',
                        method: 'GET',
                        failure: function() {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'There was an error while fetching booking events!',
                                confirmButtonColor: '#006AA0'
                            });
                        }
                    },
                    eventClick: function(info) {
                        // Show booking details in SweetAlert
                        var booking = info.event.extendedProps;
                        var startTime = info.event.start.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        var endTime = info.event.end.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                        });

                        var statusBadge = booking.status === 'upcoming' ?
                            '<span class="badge bg-success">Upcoming</span>' :
                            '<span class="badge bg-secondary">Past</span>';

                        var htmlContent = `
                            <div class="booking-details-modal">
                                <div class="row mb-3">
                                    <div class="col-12 text-center">
                                        <h5 class="text-primary mb-2">${booking.service_name}</h5>
                                        ${statusBadge}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Time:</strong><br>
                                        <span class="text-muted">${startTime} - ${endTime}</span>
                                    </div>
                                    <div class="col-6">
                                        <strong>Duration:</strong><br>
                                        <span class="text-muted">${booking.duration} minutes</span>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-6">
                                        <strong>Price:</strong><br>
                                        <span class="text-success fw-bold">$${booking.service_price}</span>
                                    </div>
                                    <div class="col-6">
                                        <strong>Provider:</strong><br>
                                        <span class="text-muted">${booking.provider_name}</span>
                                    </div>
                                </div>
                                ${booking.comments ? `
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <strong>Comments:</strong><br>
                                        <span class="text-muted">${booking.comments}</span>
                                    </div>
                                </div>
                                ` : ''}
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <strong>Booking ID:</strong><br>
                                        <span class="text-muted">${booking.booking_number}</span>
                                    </div>
                                </div>
                            </div>
                        `;

                        Swal.fire({
                            title: 'Booking Details',
                            html: htmlContent,
                            icon: 'info',
                            confirmButtonText: 'Close',
                            confirmButtonColor: '#006AA0',
                            width: '500px',
                            customClass: {
                                popup: 'booking-details-popup'
                            }
                        });
                    },
                    eventDidMount: function(info) {
                        // Add custom styling based on booking status
                        if (info.event.extendedProps.status === 'past') {
                            $(info.el).css('opacity', '0.6');
                        }
                    }
                });

                calendar.render();

                // Custom navigation buttons using jQuery
                $('#today').on('click', function() {
                    calendar.today();
                    updateDateRange();
                });

                $('#prev-week').on('click', function() {
                    calendar.prev();
                    updateDateRange();
                });

                $('#next-week').on('click', function() {
                    calendar.next();
                    updateDateRange();
                });

                // Update date range display
                function updateDateRange() {
                    var view = calendar.view;
                    var start = view.activeStart;
                    var end = new Date(view.activeEnd);
                    end.setDate(end.getDate() - 1); // Adjust end date

                    var startStr = start.toLocaleDateString('en-US', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    });
                    var endStr = end.toLocaleDateString('en-US', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    });

                    $('#date-range').text(startStr + ' - ' + endStr);
                }

                // Initial date range update
                updateDateRange();

                // Handle inline calendar date selection using jQuery
                $('#apply-btn').on('click', function() {
                    if (selectedRange.length === 2) {
                        var startDate = selectedRange[0];
                        var endDate = selectedRange[1];

                        // Navigate calendar to selected date range
                        calendar.gotoDate(startDate);
                        updateDateRange();
                    } else if (selectedRange.length === 1) {
                        // Single date selected
                        calendar.gotoDate(selectedRange[0]);
                        updateDateRange();
                    }
                });

                $('#cancel-btn').on('click', function() {
                    if (typeof calendarInstance !== 'undefined') {
                        calendarInstance.clear();
                    }
                    selectedRange = [];
                });
            });
        </script>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/customer/customer-booking.blade.php ENDPATH**/ ?>