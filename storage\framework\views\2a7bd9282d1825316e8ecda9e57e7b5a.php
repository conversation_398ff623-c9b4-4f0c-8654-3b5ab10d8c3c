<!-- Body Specifications Modal -->
<div class="modal fade card-details" id="personal-info-modal" aria-labelledby="personal-info-modal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Edit Personal Info</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="personal-info-form" action="<?php echo e(route('personal-info.update')); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <label for="specification-image" class="form-label form-input-labels">Image</label>
                            <div class="position-relative form-add-services">

                                <div class="image-input image-input-empty image-input-circle" data-kt-image-input="true"
                                    style="background-image: url(<?php echo e(asset('website') . '/' . auth()->user()->profile?->pic ?? 'images/image_input_holder.png'); ?>)">
                                    <div class="image-input-wrapper w-125px h-125px">
                                    </div>
                                    <label
                                        class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <input type="file" name="image" accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                    </label>
                                    <span
                                        class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="specification-name" class="form-label form-input-labels">Full Name</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter name"
                                id="name" name="name" value="<?php echo e($user->name ?? ''); ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="specification-name" class="form-label form-input-labels">Email</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter email"
                                id="email" name="email" value="<?php echo e($user->email ?? ''); ?>">
                        </div>
                        
                        <div class="col-md-6">
                            <label for="specification-name" class="form-label form-input-labels">Location</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter location"
                                id="location" name="location" value="<?php echo e($user->profile->location ?? ''); ?>">
                        </div>
                        <?php if(auth()->user()->hasAnyRole(['business', 'individual', 'professional'])): ?>
                            <div class="col-md-6">
                                <label for="specification-name" class="form-label form-input-labels">Phone</label>
                                <input type="text" class="form-control form-inputs-field"
                                    placeholder="Enter location" name="phone"
                                    value="<?php echo e($user->profile->phone ?? ''); ?>">
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn ">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/profile_settings/modal/edit-personal-info-modal.blade.php ENDPATH**/ ?>