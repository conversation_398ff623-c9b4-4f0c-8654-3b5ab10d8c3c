<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid subscription">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-10">
                <div class="col-md-12">
                    <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                        <h6 class="sora black">Subscription Management</h6>
                    <?php endif; ?>
                    <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                        <h6 class="sora black">Subscription</h6>
                    <?php endif; ?>
                    <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                </div>
                <div class="col-md-12">
                    <div class="Subscription-tabs">
                        <ul class="nav mb-5" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="Individual-tab" data-bs-toggle="tab"
                                    data-bs-target="#Individual" type="button" role="tab" aria-controls="Individual"
                                    aria-selected="true">
                                    <p class="fs-14 fw-500 mb-0">Individual</p>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="business-tab" data-bs-toggle="tab" data-bs-target="#business"
                                    type="button" role="tab" aria-controls="business" aria-selected="false">
                                    <p class="fs-14 fw-500 mb-0">Business</p>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="enterprise-tab" data-bs-toggle="tab"
                                    data-bs-target="#enterprise" type="button" role="tab" aria-controls="enterprise"
                                    aria-selected="false">
                                    <p class="fs-14 fw-500 mb-0">Enterprise</p>
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content " id="myTabContent">
                            <div class="tab-pane fade show active" id="Individual" role="tabpanel"
                                aria-labelledby="Individual-tab">
                                <div class="card_wrapper row">
                                    <?php $__currentLoopData = $individualSubscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $individualSubscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-4">
                                            <div class="card pricing-card">
                                                <div
                                                    class="pricing-header d-flex justify-content-between align-item-center">
                                                    <h5 class="fs-32 semi_bold sora card-text-blue">
                                                        <i class="fa-solid fa-star"></i>
                                                        <?php echo e($individualSubscription->name ?? ''); ?>

                                                    </h5>
                                                </div>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                                                    <?php if(auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $individualSubscription->id): ?>
                                                        <span class="badge current-plan-badge deep-blue fs-14 bold">
                                                            Current Plan
                                                        </span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <h2 class="semi_bold black sora">
                                                    $<?php echo e($individualSubscription->price ?? ''); ?><span
                                                        class="fs-18 light-black normal sora">/month</span></h2>
                                                <div class="card-body">
                                                    <ul class="features">
                                                        <?php $__currentLoopData = $individualSubscription->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <li><?php echo e($detail->feature ?? ''); ?></li>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </ul>
                                                </div>
                                                <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                    <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                        class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                        Package</a>
                                                <?php endif; ?>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                    <?php if(auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $individualSubscription->id): ?>
                                                        <form action="<?php echo e(route('subscription.cancel')); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <input type="hidden" name="stripe_subscription_id"
                                                                value="<?php echo e(auth()->user()->activeSubscription->stripe_subscription_id ?? ''); ?>">
                                                            <button class="cancel-link fs-16 semi_bold inter"
                                                                type="submit">
                                                                Cancel Your Plan</button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form action="<?php echo e(route('payment.subscription')); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <input type="hidden" name="subscription_id"
                                                                value="<?php echo e($individualSubscription->id); ?>">
                                                            <button class="subscribed-label fs-16 semi_bold inter black"
                                                                type="submit">Subscribe</button>
                                                        </form>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>


                            <div class="tab-pane fade show" id="business" role="tabpanel" aria-labelledby="business-tab">
                                <div class="card_wrapper row">
                                    <?php $__currentLoopData = $businessSubscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $businessSubscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-4">
                                            <div class="card pricing-card">
                                                <div
                                                    class="pricing-header d-flex justify-content-between align-item-center">
                                                    <h5 class="fs-32 semi_bold sora card-text-blue">
                                                        <i class="fa-solid fa-star"></i>
                                                        <?php echo e($businessSubscription->name ?? ''); ?>

                                                    </h5>
                                                </div>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                                                    <?php if(auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $businessSubscription->id): ?>
                                                        <span class="badge current-plan-badge deep-blue fs-14 bold">
                                                            Current Plan
                                                        </span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <h2 class="semi_bold black sora">$<?php echo e($businessSubscription->price ?? ''); ?>

                                                    <span class="fs-18 light-black normal sora">/month</span>
                                                </h2>
                                                <div class="card-body">
                                                    <ul class="features">
                                                        <?php $__currentLoopData = $businessSubscription->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <li><?php echo e($detail->feature ?? ''); ?></li>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </ul>
                                                </div>
                                                <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                    <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                        class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                        Package</a>
                                                <?php endif; ?>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                    <?php if(auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $businessSubscription->id): ?>
                                                        <form action="<?php echo e(route('subscription.cancel')); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <input type="hidden" name="stripe_subscription_id"
                                                                value="<?php echo e(auth()->user()->activeSubscription->stripe_subscription_id ?? ''); ?>">
                                                            <button class="cancel-link fs-16 semi_bold inter"
                                                                type="submit">
                                                                Cancel Your Plan</button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form action="<?php echo e(route('payment.subscription')); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <input type="hidden" name="subscription_id"
                                                                value="<?php echo e($businessSubscription->id); ?>">
                                                            <button class="subscribed-label fs-16 semi_bold inter black"
                                                                type="submit">Subscribe</button>
                                                        </form>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>


                            <div class="tab-pane fade show " id="enterprise" role="tabpanel"
                                aria-labelledby="enterprise-tab">
                                <div class="card_wrapper row">
                                    <?php $__currentLoopData = $enterpriseSubscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enterpriseSubscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-4">
                                            <div class="card pricing-card">
                                                <div
                                                    class="pricing-header d-flex justify-content-between align-item-center">
                                                    <h5 class="fs-32 semi_bold sora card-text-blue">
                                                        <i class="fa-solid fa-star"></i>
                                                        <?php echo e($enterpriseSubscription->name ?? ''); ?>

                                                    </h5>
                                                </div>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                                                    <?php if(auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $enterpriseSubscription->id): ?>
                                                        <span class="badge current-plan-badge deep-blue fs-14 bold">
                                                            Current Plan
                                                        </span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <h2 class="semi_bold black sora">
                                                    $<?php echo e($enterpriseSubscription->price ?? ''); ?>

                                                    <span class="fs-18 light-black normal sora">/month</span>
                                                </h2>
                                                <div class="card-body">
                                                    <ul class="features">
                                                        <?php $__currentLoopData = $enterpriseSubscription->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <li><?php echo e($detail->feature ?? ''); ?></li>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </ul>
                                                </div>
                                                <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                                    <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                        class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                        Package</a>
                                                <?php endif; ?>
                                                <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional')): ?>
                                                    <?php if(auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $enterpriseSubscription->id): ?>
                                                        <form action="<?php echo e(route('subscription.cancel')); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <input type="hidden" name="stripe_subscription_id"
                                                                value="<?php echo e(auth()->user()->activeSubscription->stripe_subscription_id ?? ''); ?>">
                                                            <button class="cancel-link fs-16 semi_bold inter"
                                                                type="submit">
                                                                Cancel Your Plan</button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form action="<?php echo e(route('payment.subscription')); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <input type="hidden" name="subscription_id"
                                                                value="<?php echo e($enterpriseSubscription->id); ?>">
                                                            <button class="subscribed-label fs-16 semi_bold inter black"
                                                                type="submit">Subscribe</button>
                                                        </form>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php echo $__env->make('dashboard.subscription.modal.edit-package-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->stopSection(); ?>
    <?php $__env->startPush('js'); ?>
        <script>
            $(document).ready(function() {
                // Get stored tab
                var activeTab = localStorage.getItem('activeTab');
                if (activeTab) {
                    var tabElement = $('#myTab button[data-bs-target="' + activeTab + '"]');
                    if (tabElement.length) {
                        $('#myTab button').removeClass('active');
                        $('.tab-pane').removeClass('show active');

                        tabElement.addClass('active').attr('aria-selected', 'true');
                        $(activeTab).addClass('show active');
                    }
                }
                $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function(event) {
                    var target = $(event.target).attr('data-bs-target');
                    localStorage.setItem('activeTab', target);
                });
            });
        </script>

        <script>
            $(document).ready(function() {
                function updateModalForActiveTab() {
                    var activeTab = $('#myTab .nav-link.active');
                    if (activeTab.length > 0) {
                        var tabText = activeTab.find('p').text().trim();
                        var tabValue = tabText.toLowerCase();

                        $('#package-heading').html('<i class="fa-solid fa-star"></i> ' + tabText);
                        $('#package-type').val(tabValue);
                    }
                }
                // Update modal when it's shown
                $('#edit_package').on('show.bs.modal', function() {
                    updateModalForActiveTab();
                });
                $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function() {
                    updateModalForActiveTab();
                });
                updateModalForActiveTab();
            });
        </script>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/subscription/subscription.blade.php ENDPATH**/ ?>