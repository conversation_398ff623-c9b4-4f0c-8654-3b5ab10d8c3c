@extends('website.layout.master')

@push('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css" />
    <style>
        /* Table container */
        .table-container {
            background: transparent;
            border-radius: 12px;
            overflow: hidden;
            padding: 5px;
        }

        /* DataTable styles */
        #responsiveTable {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0 10px;
            border: none;
        }

        /* Hide the original header */
        #responsiveTable thead {
            display: none;
        }

        #responsiveTable td {
            padding: 20px;
            vertical-align: middle;
            background: white;
            position: relative;
            border: 1px solid #f0f0f0;
            border-right: none;
            border-left: none;
            box-shadow: none;
            font-size: 15px;
            font-weight: 400;
            color: #000;
        }

        /* Add column header before each cell */
        #responsiveTable td::before {
            content: attr(data-label);
            display: block;
            font-weight: 500;
            color: #9a9ea6;
            margin-bottom: 5px;
            font-size: 12px;
        }

        #responsiveTable tr {
            transition: all 0.3s ease;
            box-shadow: 0px 1.13px 2.25px 0px #0000000d;
            border-radius: 8px;
        }

        /* Add border radius to first and last td in each row */
        #responsiveTable tr td:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
            border-right: none;
            border-left: 1px solid #f0f0f0;
        }

        #responsiveTable tr td:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            border-left: none;
            border-right: 1px solid #f0f0f0;
        }

        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #responsiveTable tr {
                display: block;
                margin-bottom: 15px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0px 1.13px 2.25px 0px #0000000d;
            }

            #responsiveTable td {
                display: block;
                padding: 12px 15px;
                text-align: left;
                border-radius: 0 !important;
                border-right: 1px solid #f0f0f0;
                border-left: 1px solid #f0f0f0;
                border-top: none;
                border-bottom: none;
            }

            #responsiveTable td::before {
                content: attr(data-label);
                font-weight: bold;
                margin-right: 15px;
                text-align: left;
                color: #555;
                display: inline-block;
                width: 100px;
            }

            #responsiveTable tr td:first-child {
                border-top-left-radius: 8px !important;
                border-top-right-radius: 8px !important;
                border-bottom-left-radius: 0 !important;
            }

            #responsiveTable tr td:last-child {
                border-bottom-left-radius: 8px !important;
                border-bottom-right-radius: 8px !important;
                border-top-right-radius: 0 !important;
            }

            .dataTables_info,
            .dataTables_paginate {
                text-align: center !important;
                float: none !important;
            }
        }

        /* Pagination styles */
        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 6px 12px;
            margin: 0 3px;
            border-radius: 6px;
            border: 1px solid #ddd;
            transition: all 0.2s;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #f1f1f1;
            border-color: #ccc;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #020C87;
            color: white !important;
            border-color: #020C87;
        }

        .search_box {position: relative;}

        .search_box .search_input {
            height: 36px;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            min-width: 210px;
            outline: none;
            padding-left: 30px;}

        .search_box svg {
            position: absolute;
            top: 10px;
            left: 10px;
        }
    </style>
@endpush

@section('content')
    <section>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12"><h1>Dashboard</h1></div>
            </div>
        </div>
    </section>

    <section>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="table-container">
                        <div class="table_top">
                            <div class="search_box">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M14.75 14.75L10.25 10.25M11.75 6.5C11.75 7.18944 11.6142 7.87213 11.3504 8.50909C11.0865 9.14605 10.6998 9.7248 10.2123 10.2123C9.7248 10.6998 9.14605 11.0865 8.50909 11.3504C7.87213 11.6142 7.18944 11.75 6.5 11.75C5.81056 11.75 5.12787 11.6142 4.49091 11.3504C3.85395 11.0865 3.2752 10.6998 2.78769 10.2123C2.30018 9.7248 1.91347 9.14605 1.64963 8.50909C1.3858 7.87213 1.25 7.18944 1.25 6.5C1.25 5.10761 1.80312 3.77226 2.78769 2.78769C3.77226 1.80312 5.10761 1.25 6.5 1.25C7.89239 1.25 9.22774 1.80312 10.2123 2.78769C11.1969 3.77226 11.75 5.10761 11.75 6.5Z" stroke="#9A9EA6" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <input class="search_input" type="text" id="customSearchInput" placeholder="Search..." />
                            </div>
                        </div>
                        <table id="responsiveTable" class="display nowrap" style="width: 100%">
                            <thead>
                            <tr>
                                <th>Name</th>
                                <th>Position</th>
                                <th>Office</th>
                                <th>Age</th>
                                <th>Start Date</th>
                                <th>Salary</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td data-label="Name">Tiger Nixon</td>
                                <td data-label="Position">System Architect</td>
                                <td data-label="Office">Edinburgh</td>
                                <td data-label="Age">61</td>
                                <td data-label="Start Date">2011/04/25</td>
                                <td data-label="Salary">$320,800</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Garrett Winters</td>
                                <td data-label="Position">Accountant</td>
                                <td data-label="Office">Tokyo</td>
                                <td data-label="Age">63</td>
                                <td data-label="Start Date">2011/07/25</td>
                                <td data-label="Salary">$170,750</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Ashton Cox</td>
                                <td data-label="Position">Junior Technical Author</td>
                                <td data-label="Office">San Francisco</td>
                                <td data-label="Age">66</td>
                                <td data-label="Start Date">2009/01/12</td>
                                <td data-label="Salary">$86,000</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Cedric Kelly</td>
                                <td data-label="Position">Senior Javascript Developer</td>
                                <td data-label="Office">Edinburgh</td>
                                <td data-label="Age">22</td>
                                <td data-label="Start Date">2012/03/29</td>
                                <td data-label="Salary">$433,060</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Airi Satou</td>
                                <td data-label="Position">Accountant</td>
                                <td data-label="Office">Tokyo</td>
                                <td data-label="Age">33</td>
                                <td data-label="Start Date">2008/11/28</td>
                                <td data-label="Salary">$162,700</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Tiger Nixon</td>
                                <td data-label="Position">System Architect</td>
                                <td data-label="Office">Edinburgh</td>
                                <td data-label="Age">61</td>
                                <td data-label="Start Date">2011/04/25</td>
                                <td data-label="Salary">$320,800</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Garrett Winters</td>
                                <td data-label="Position">Accountant</td>
                                <td data-label="Office">Tokyo</td>
                                <td data-label="Age">63</td>
                                <td data-label="Start Date">2011/07/25</td>
                                <td data-label="Salary">$170,750</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Ashton Cox</td>
                                <td data-label="Position">Junior Technical Author</td>
                                <td data-label="Office">San Francisco</td>
                                <td data-label="Age">66</td>
                                <td data-label="Start Date">2009/01/12</td>
                                <td data-label="Salary">$86,000</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Cedric Kelly</td>
                                <td data-label="Position">Senior Javascript Developer</td>
                                <td data-label="Office">Edinburgh</td>
                                <td data-label="Age">22</td>
                                <td data-label="Start Date">2012/03/29</td>
                                <td data-label="Salary">$433,060</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Airi Satou</td>
                                <td data-label="Position">Accountant</td>
                                <td data-label="Office">Tokyo</td>
                                <td data-label="Age">33</td>
                                <td data-label="Start Date">2008/11/28</td>
                                <td data-label="Salary">$162,700</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Tiger Nixon</td>
                                <td data-label="Position">System Architect</td>
                                <td data-label="Office">Edinburgh</td>
                                <td data-label="Age">61</td>
                                <td data-label="Start Date">2011/04/25</td>
                                <td data-label="Salary">$320,800</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Garrett Winters</td>
                                <td data-label="Position">Accountant</td>
                                <td data-label="Office">Tokyo</td>
                                <td data-label="Age">63</td>
                                <td data-label="Start Date">2011/07/25</td>
                                <td data-label="Salary">$170,750</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Ashton Cox</td>
                                <td data-label="Position">Junior Technical Author</td>
                                <td data-label="Office">San Francisco</td>
                                <td data-label="Age">66</td>
                                <td data-label="Start Date">2009/01/12</td>
                                <td data-label="Salary">$86,000</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Cedric Kelly</td>
                                <td data-label="Position">Senior Javascript Developer</td>
                                <td data-label="Office">Edinburgh</td>
                                <td data-label="Age">22</td>
                                <td data-label="Start Date">2012/03/29</td>
                                <td data-label="Salary">$433,060</td>
                            </tr>
                            <tr>
                                <td data-label="Name">Airi Satou</td>
                                <td data-label="Position">Accountant</td>
                                <td data-label="Office">Tokyo</td>
                                <td data-label="Age">33</td>
                                <td data-label="Start Date">2008/11/28</td>
                                <td data-label="Salary">$162,700</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('js')
    <script>
        $(document).ready(function () {
            var table = $("#responsiveTable").DataTable({
                dom: "rtip",
                responsive: true,
                scrollX: false,
                paging: true,
                pageLength: 5,
                lengthChange: false,
                initComplete: function () {
                    // Set data-label attributes from header text
                    this.api()
                        .columns()
                        .header()
                        .each(function (header) {
                            var title = $(header).text();
                            $(header).attr("data-label", title);
                        });
                },
            });



            // Update data-labels when table changes
            table.on("draw", function () {
                $("th").each(function () {
                    var title = $(this).text();
                    $(this).attr("data-label", title);
                });
            });

            $("#customSearchInput").on("keyup", function () {
                table.search(this.value).draw();
            });
        });
    </script>
@endpush