<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid booking">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12">
                    <h6 class="semi_bold sora black">Earnings</h6>
                    <p class="fs-14 normal sora light-black">Lorem ipsum dolor sit amet consectetur. </p>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper">
                <div class="col-xl-6 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-deep-blue">
                                    <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-50">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Earnings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="2420" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer ">
                                <div class="fs-12 w-700 green-box green">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-6 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    <?php echo $__env->make('svg.earning', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-50 ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Payment Being Cleared
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="1869" data-kt-countup-prefix="$">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div class="fs-12 w-700 green-box green">
                                    <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                    17.2%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                            <!-- Select with dots -->
                            <div class="dropdown search_box select-box">
                                <button
                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span><span class="dot"></span>
                                        All</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                            data-color="#4B5563"><span class="dot all"></span>
                                            All</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                            data-color="#F59E0B"><span class="dot ongoing"></span>
                                            Ongoing</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                            data-color="#3B82F6"><span class="dot upcoming"></span>
                                            Upcoming</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                            data-color="#10B981"><span class="dot completed"></span>
                                            Complete</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                            data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                            Canceled</a></li>
                                </ul>
                            </div>

                            <!-- category -->
                            <div class="search_box select-box">
                                <select class="search_input">
                                    <option value="Category">Category</option>
                                    <option value="all">All</option>
                                    <option value=" Group"> Group</option>
                                    <option value="Individual">Individual</option>
                                </select>
                            </div>
                            <!-- Date Picker -->

                            <label for="datePicker" class="date_picker">
                                <div class="date-picker-container">
                                    <i class="bi bi-calendar-event calender-icon"></i>
                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                </div>
                            </label>
                            <div class="search_box d-block ms-auto">
                                <a href="#!" class="search_input fs-14 normal link-gray ">
                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                </a>
                            </div>

                        </div>
                        <table id="responsiveTable" class="responsiveTable display nowrap w-100">
                            <thead>
                                <tr>
                                    <th>Customer Name</th>
                                    <th>Service Type</th>
                                    <th>Client Name</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php for($b = 0; $b < 20; $b++): ?>
                                    <tr>
                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                        <td data-label="Service Typee">Hair Coloring</td>
                                        <td data-label="Client">Walk-in</td>
                                        <td data-label="Amount">$17.84</td>
                                        <td data-label="Status" class="status paid-status">Booked</td>
                                        <td data-label="Date">Apr 10, 2025 </td>
                                        <td data-label="Action">
                                            <div class="dropdown">
                                                <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <button class="dropdown-item complete fs-14 regular " type="button">
                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                            Mark as Complete
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item cancel fs-14 regular" type="button">
                                                            <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                        <td data-label="Service Typee">Hair Coloring</td>
                                        <td data-label="Client Name">Walk-in</td>
                                        <td data-label="Amount">$17.84</td>
                                        <td data-label="Status" class="status unpaid-status">Cancelled</td>
                                        <td data-label="Date">Apr 10, 2025 </td>
                                        <td data-label="Action">
                                            <div class="dropdown">
                                                <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    <li>
                                                        <button class="dropdown-item complete fs-14 regular " type="button">
                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                            Mark as Complete
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button class="dropdown-item cancel fs-14 regular" type="button">
                                                            <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endfor; ?>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/earning.blade.php ENDPATH**/ ?>