<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
  <g clip-path="url(#clip0_730_37164)">
    <path d="M13.4167 24.5007H7.00002C6.38118 24.5007 5.78769 24.2548 5.3501 23.8172C4.91252 23.3796 4.66669 22.7862 4.66669 22.1673V8.16732C4.66669 7.54848 4.91252 6.95499 5.3501 6.5174C5.78769 6.07982 6.38118 5.83398 7.00002 5.83398H21C21.6189 5.83398 22.2124 6.07982 22.6499 6.5174C23.0875 6.95499 23.3334 7.54848 23.3334 8.16732V15.1673" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M18.6667 3.5V8.16667" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M9.33331 3.5V8.16667" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M4.66669 12.834H23.3334" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M17.5 22.1673L19.8333 24.5007L24.5 19.834" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  <defs>
    <clipPath id="clip0_730_37164">
      <rect width="28" height="28" fill="white"/>
    </clipPath>
  </defs>
</svg><?php /**PATH D:\SALMAN\git\anders\resources\views/svg/booking.blade.php ENDPATH**/ ?>