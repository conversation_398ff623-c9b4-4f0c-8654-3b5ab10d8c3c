<div class="modal fade card-details" id="edit-product-certifications-modal" aria-labelledby="edit-product-certifications-modal"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Edit Product Certifications</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-product-certifications-form" action="<?php echo e(route('product-certifications.update')); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <div class="custom-checkbox-group mb-3 gray-card">
                                <?php $__currentLoopData = $product_certifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $certification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="product_certifications[]"
                                            value="<?php echo e($certification->id); ?>"
                                            <?php echo e(auth()->user()->product_cerficates->contains($certification->id) ? 'checked' : ''); ?>>
                                        <span class="checkbox-label">
                                            <img src="<?php echo e(asset('website') . '/' . $certification->image); ?>" style="border-radius: 50%" width="30" height="30" alt="">
                                        </span>
                                        <?php echo e($certification->name ?? ''); ?>

                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Save</button>
                </div>
            </form>
        </div>
        </div>
    </div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/profile_settings/modal/edit-product-certifications-modal.blade.php ENDPATH**/ ?>