<div id="kt_app_sidebar nnnnn" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar"
    data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px"
    data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">

        <a class="d-flex align-items-center gap-3" href="<?php echo e(url('/')); ?>">

            <img src="<?php echo e(asset('website')); ?>/assets/images/footer_primary.svg"
                class="h-300px w-225px object-fit-contain rounded-pill top-rated-image" alt="card-image">

            <!-- <img src="<?php echo e(asset('website') . '/' . setting()->logo); ?>" alt="Logo" class="img-fluid"
                style="height: 50px;"> <span class="deep-blue fs-32 bold">Keldaa</span> -->


            <!-- <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon ?? ''); ?>"
                class="h-50px app-sidebar-logo-minimize" /> -->
        </a>

        <!-- begin::Minimized sidebar setup:
     if (isset($_COOKIE["sidebar_minimize_state"]) && $_COOKIE["sidebar_minimize_state"] === "on") {
     1. "src/js/layout/sidebar.js" adds "sidebar_minimize_state" cookie value to save the sidebar minimize state.
     2. Set data-kt-app-sidebar-minimize="on" attribute for body tag.
     3. Set data-kt-toggle-state="active" attribute to the toggle element with "kt_app_sidebar_toggle" id.
     4. Add "active" class to to sidebar toggle element with "kt_app_sidebar_toggle" id.
     }
        <div id="kt_app_sidebar_toggle"
            class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary h-30px w-30px position-absolute top-50 start-100 translate-middle rotate"
            data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body"
            data-kt-toggle-name="app-sidebar-minimize">
            <i class="ki-duotone ki-black-left-line fs-3 rotate-180">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>
        </div>  -->
    </div>
    <div class="app-sidebar-menu overflow-hidden flex-column-fluid admin-sidebar ">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper ">
            <div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu"
                    data-kt-menu="true" data-kt-menu-expand="false">
                    <?php if(Auth::user()->hasRole('developer')): ?>
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                            </div>
                        </div>
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">User Management</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('crud-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">CRUD</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('crud_generator')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">CRUD Generator</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Users</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('users')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Users List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Roles</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('roles')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Permissions</span>
                                    </a>
                                </div>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-list')): ?>
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo e(url('settings')); ?>">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Settings</span>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr>
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Menu</span>
                            </div>
                        </div>
                        <?php $__currentLoopData = $crud; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($item->url . '-list')): ?>
                                
                                <div class="menu-item">
                                    <a class="menu-link <?php echo e(request()->is($item->url) ? 'active' : ''); ?>"
                                        href="<?php echo e(url($item->url ?? 'home')); ?>">
                                        <span class="menu-icon">
                                            <i class="ki-duotone ki-abstract-28 fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span
                                            class="menu-title"><?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name)); ?></span>
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                    <?php if(Auth::user()->hasRole('admin')): ?>
                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('dashboard') ? 'active' : ''); ?>"
                                href="<?php echo e(route('dashboard')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-qrcode"></i></span>
                                <span class="menu-title">Dashboard</span>
                            </a>
                        </div>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('categories-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*categories*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('categories.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-regular fa-file"></i></span>
                                    <span class="menu-title">Categories</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*professionals*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('professionals')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-users"></i></span>
                                <span class="menu-title">Professionals</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*customers*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('customers')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-money-bill-1"></i></span>
                                <span class="menu-title">Customers</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*services*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('services.index')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-money-bill-1"></i></span>
                                <span class="menu-title">Services</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*booking*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('booking')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-laptop"></i></span>
                                <span class="menu-title">Bookings</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*refund_request*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('refund_request')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-file-invoice"></i></span>
                                <span class="menu-title">Refund Requests</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*wallet*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('wallet')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                <span class="menu-title">Wallet</span>
                            </a>
                        </div>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('vatmanagements-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*vatmanagements*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('vatmanagements.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">VAT Management</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('discountcoupons-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*discount-coupons*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('discount-coupons.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Discount & Coupon</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscriptions-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*subscriptions*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('subscriptions.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Subscription Management</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('holidays-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*holidays*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('holidays.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Holidays</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('certifications-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*certifications*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('certifications.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Certifications</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cms-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*cms*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('cms.home')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">CMS</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('countries-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*countries*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('countries.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Countries</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('social-platforms-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*social-platforms*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('social-platforms.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Social Platforms</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('*navigation-links*') ? 'active' : ''); ?>"
                                href="<?php echo e(route('navigation-links.index')); ?>">
                                <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                <span class="menu-title">Navigation Links</span>
                            </a>
                        </div>


                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link <?php echo e(request()->is('*settings*') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('settings.index')); ?>">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Settings</span>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php if(Auth::user()->hasRole('developer')): ?>
        <div class="app-sidebar-footer flex-column-auto pt-2 pb-6 px-6" id="kt_app_sidebar_footer">
            <a href="<?php echo e(url('html/demo1/dist')); ?>"
                class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap px-0 h-40px w-100"
                data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click"
                title="200+ in-house components and 3rd-party plugins">
                <span class="btn-label">Docs & Components</span>
                <i class="ki-duotone ki-document btn-icon fs-2 m-0">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
            </a>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/layout/sidebar.blade.php ENDPATH**/ ?>