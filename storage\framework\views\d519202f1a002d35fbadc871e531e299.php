<div class="modal fade card-details edit-certificationsmodal" id="edit-certifications&licenses-modal"
    aria-labelledby="edit-certifications&licenses-modal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Edit Certifications & Licenses</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-certifications&licenses-form" action="<?php echo e(route('certificates.save')); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body share-certificates">
                    <div class="row row-gap-5">
                        <?php if(auth()->user()->certificates->count() == 0): ?>
                            <div class="gray-card mt-5 file-upload-group">
                                <div class="row">
                                    <h6 class="mb-8 text-center"> Certificate #1 </h6>
                                    <div class="col-md-12 mb-4">
                                        <label class="fieldlabels">Certification Title*</label>
                                        <input class="w-100" type="text" name="certificates[0][title]" placeholder="Enter certification title">
                                    </div>
                                    
                                    <div class="col-md-12 mb-4">
                                        <label class="fieldlabels">Issued by*</label>
                                        <input class="w-100" type="text" name="certificates[0][issued_by]"  placeholder="Enter name">
                                    </div>

                                    <div class="col-md-6">
                                        <label class="fieldlabels">Issued Date*</label>
                                        <input type="date" name="certificates[0][issued_date]"
                                            placeholder="Enter certification title">
                                    </div>

                                    <div class="col-md-6">
                                        <label class="fieldlabels">End Date*</label>
                                        <input type="date" name="certificates[0][end_date]"
                                            placeholder="Enter certification title">
                                    </div>

                                    <div class="col-md-12 form-border">
                                        <p class="manrope fw-600 light-black">Share Certificates</p>
                                        <div class="file-upload-group">
                                            <div class="file-upload-group">
                                                <label class="upload-box">
                                                    <img src="http://127.0.0.1:8000/website/assets/images/upload.svg"
                                                        alt="Upload Icon">
                                                    <p>Upload Certificate</p>
                                                    <p class="mb-0">Maximum file size: 2 MB</p>
                                                    <p>Supported format: JPG and PNG</p>
                                                    <span class="add-file">
                                                        <p class="upload-cert-btn no_validate">Upload</p>
                                                    </span>
                                                    <input type="file" name="certificates[0][image]"
                                                        class="file-input no_validate" hidden="">
                                                </label>
                                                <div class="preview-container"></div>
                                            </div>
                                        </div>


                                        <div class="exception-checkbox">
                                            <label class="cert-excep">
                                                <input type="checkbox" name="certificates[0][exception]"
                                                    id="exceptionToggle">
                                                <span class="">Certificate Exception</span>
                                            </label>

                                            <div class="exception-textarea">
                                                <label class="mb-2" for="w3review">Reason for Exception</label>
                                                <textarea class="mb-0 no_validate" id="w3review" name="certificates[0][exception_reason]" rows="4" cols="50"
                                                    placeholder="Write reason for exception"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3 d-flex justify-content-between">
                                        <button type="button" class="delete-block" style="display: none;">Delete This
                                            Block</button>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="col-md-12">
                            <div id="certifications-wrapper">
                                <?php $__currentLoopData = auth()->user()->certificates ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class=" gray-card my-5 file-upload-group">
                                        <h6 class="mb-8"> Certificate #1 </h6>
                                        

                                        <div class="row">
                                            <div class="col-md-12 mb-4">
                                                <label class="fieldlabels">Certification Title*</label>
                                                <input class="w-100" type="text" name="certificates[<?php echo e($index); ?>][title]"
                                                    placeholder="Enter certification title"
                                                    value="<?php echo e($certificate->title); ?>">
                                            </div>

                                            <div class="col-md-12 mb-4">
                                                <label class="fieldlabels">Issued by*</label>
                                                <input class="no_validate w-100" type="text"
                                                    name="certificates[<?php echo e($index); ?>][issued_by]"
                                                    value="<?php echo e($certificate->issued_by); ?>" placeholder="Enter name">
                                            </div>

                                            <div class="col-md-6">
                                                <label class="fieldlabels">Issued Date*</label>
                                                <input class="no_validate w-100" type="date"
                                                    name="certificates[<?php echo e($index); ?>][issued_date]"
                                                    value="<?php echo e($certificate->issued_date); ?>" placeholder="Enter issued date">
                                            </div>

                                            <div class="col-md-6">
                                                <label class="fieldlabels">End Date*</label>
                                                <input class="no_validate w-100" type="date"
                                                    name="certificates[<?php echo e($index); ?>][end_date]"
                                                    value="<?php echo e($certificate->end_date); ?>" placeholder="Enter end date">
                                            </div>

                                            <div class="col-md-12 form-border">
                                                <p class="manrope fw-600 light-black">Share Certificates</p>
                                                <div class="share-certificates">
                                                    <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans"
                                                        style="cursor:pointer;">
                                                        <img src="<?php echo e(asset('website') . '/' . $certificate->image); ?>"
                                                            alt="Upload Icon">
                                                        <p>Upload Certificate</p>
                                                        <p class="mb-0">Maximum file size: 2 MB</p>
                                                        <p>Supported format: JPG and PNG</p>
                                                        <span class="add-file">
                                                            <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                                        </span>
                                                        <input class="no_validate"
                                                            name="certificates[<?php echo e($index); ?>][image]"
                                                            type="file" hidden="">
                                                        <?php if($certificate->image): ?>
                                                            <input type="hidden"
                                                                name="certificates[<?php echo e($index); ?>][old_image]"
                                                                value="<?php echo e($certificate->image); ?>">
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                                <div class="preview-container"></div>

                                                <div class="exception-checkbox mt-5">
                                                    <label class="cert-excep">
                                                        <input class="no_validate" type="checkbox" id="exceptionToggle"
                                                            name="certificates[<?php echo e($index); ?>][exception]"
                                                            <?php echo e($certificate->exception ? 'checked' : ''); ?>>
                                                        <span class="checkmark">Certificate Exception</span>
                                                    </label>

                                                    <div class="exception-textarea">
                                                        <label class="mb-2" for="w3review_1">Reason for
                                                            Exception</label>
                                                        <textarea class="mb-0 no_validate w-100" id="w3review_1" name="certificates[<?php echo e($index); ?>][exception_reason]"
                                                            rows="4" cols="50" placeholder="Write reason for exception"> <?php echo e($certificate->exception_reason); ?></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php if(!$loop->first): ?>
                                                <div class="mt-3 d-flex justify-content-between">
                                                    <button type="button" class="delete-block">Delete This Block</button>
                                                </div>
                                            <?php endif; ?>

                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <button type="button" id="addMoreBtn" class="addMoreBtn blue-text mt-3">
                                <span><i class="fas fa-plus"></i></span> Add More
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/profile_settings/modal/edit-certifications&licenses-modal.blade.php ENDPATH**/ ?>