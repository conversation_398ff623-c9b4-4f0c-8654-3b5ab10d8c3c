
<div>
    <ul class="nav nav-pills  service-subcategorymb-10" id="pills-tab" role="tablist">

        <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item" role="presentation">
                <button
                    class="nav-link <?php echo e($selectedSubcategory && $selectedSubcategory->slug == $subcategory->slug ? 'active' : ''); ?> service-tab subcategory-tab-btn"
                    id="pills-subcategory-<?php echo e($subcategory->slug); ?>-tab" data-bs-toggle="pill"
                    data-bs-target="#pills-fitness" type="button" role="tab"
                    data-category-name="<?php echo e($subcategory->category->slug); ?>"
                    data-subcategory-name="<?php echo e($subcategory->slug); ?>" aria-controls="pills-fitness"
                    aria-selected="<?php echo e($selectedSubcategory && $selectedSubcategory->slug == $subcategory->slug ? 'true' : 'false'); ?>">
                    <?php echo e($subcategory->name); ?>

                </button>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
    </ul>
    
</div>



<div class="tab-content mt-10" id="pills-tabContent  ">
    <?php
        $images = [
            'trainer1.png',
            'trainer2.png',
            'trainer3.png',
            'trainer4.png',
            'trainer5.png',
            'trainer6.png',
            'trainer7.png',
            'trainer8.png',
            'trainer9.png',
            'trainer10.png',
            'trainer11.png',
            'trainer5.png',
        ];
    ?>

    <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab"
        tabindex="0">
        <div class="tab-content mt-10" id="pills-tabContent  ">
            <div class="tab-pane fade active show" id="pills-fitness" role="tabpanel"
                aria-labelledby="pills-fitness-tab" tabindex="0">
                <div class="row row-gap-8">
                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $i = $loop->index;
                        ?>
                        <div class="col-md-3">
                            <div class="card top-rated-card services-card">
                                <div class="card-header border-0 p-0 ">
                                    <img src="<?php echo e(asset('website' . '/' . $service->image)); ?>"
                                          onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'"
                                        class=" top-rated-image" alt="card-image">
                                </div>
                                <div class="card-body p-5 bg-white">
                                    <p class="fs-16 semi_bold black m-0 "><?php echo e($service->name); ?></p>
                                    <div class="d-flex gap-2 align-items-center">
                                        <img src="<?php echo e(asset('website') . '/' . ($service?->user?->profile?->avatar ?? '')); ?>"
                                         onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'"
                                            class="rounded-pill w-25px h-25px" alt="card-image">
                                        
                                    </div>
                                </div>
                                <div class="card-footer border-0 d-flex justify-content-between p-5">
                                    <div>
                                        <p class="m-0 fs-16 black bold">$<?php echo e($service->price); ?></p>
                                        <p class="m-0 fs-14 regular "><i class="fa-regular fa-clock"></i>
                                            <?php echo e($service->duration); ?> mins</p>
                                    </div>
                                    <?php if(auth()->guard()->check()): ?>
                                        <?php if(!auth()->user()->hasRole('admin')): ?>
                                        <button class="blue-button add-to-cart-btn" data-bs-toggle="modal"
                                            data-id="<?php echo e($service->ids); ?>" data-bs-target="#service-details">Book
                                            Now</button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button onclick="window.location.href='<?php echo e(route('register')); ?>'" class="blue-button add-to-cart-btn">Login</button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

</div>

<?php $__env->startPush('js'); ?>

    <script>
        $(document).ready(function () {
            $('.daterange').daterangepicker();
        });
    </script>

    <script>
        $(document).ready(function () {
            $('input[name="loc"]').on('change', function () {
                var selectedValue = $('input[name="loc"]:checked').val();

                if (selectedValue === "Providers location") {
                    $('#providers-loc').show();
                    $('#home-loc').hide();
                } else if (selectedValue === "Your Home/Office") {
                    $('#providers-loc').hide();
                    $('#home-loc').show();
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php /**PATH D:\SALMAN\git\anders\resources\views/website/template/subcategory-service.blade.php ENDPATH**/ ?>