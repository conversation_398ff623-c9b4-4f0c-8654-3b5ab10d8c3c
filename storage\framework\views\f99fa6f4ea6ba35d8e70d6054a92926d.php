<?php $__env->startPush('css'); ?>
    <style>

    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-home notification-sec">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row justify-content-center">
                <div class="col-md-10 ">
                    <div class="card card-box notification-bg p-0">
                        <div
                            class="card-header notification-header d-flex justify-content-between align-items-center p-4 flex-wrap gap-2">
                            <p class="fs-18 semi_bold sora mb-0">Notifications</p>
                            <div class="d-flex align-items-center gap-3">
                                <!-- Mark all as read -->
                                <a href="#" class="deep-blue fs-16 semi_bold">Mark all as read</a>
                                <div class=" p-0">
                                    <!-- Filter dropdown -->
                                    <select class="form-select filter-select filter-dropdown" id="filter-category"
                                        data-control="select2" data-placeholder="Select category" data-hide-search="true"
                                        data-dropdown-css-class="w-200px">
                                        <option value="filters" selected disabled hidden>Filters</option>
                                        <option value="all">All</option>
                                        <option value="new-bookings">New Bookings</option>
                                        <option value="reschedule">Reschedule</option>
                                        <option value="canceled">Canceled</option>
                                        <option value="reviews">Reviews</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="scrollbar ">
                                <div class="row row-gap-2">
                                    <?php $__empty_1 = true; $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <div class="col-lg-12">
                                            <div
                                                class="card noti-content flex-row gap-5 align-items-center justify-content-center shadow-none">
                                                <div class="card-header profile-img border-0 align-items-center">
                                                    <img src="<?php echo e(asset('website').'/'.($notification->user->profile->pic ?? 'assets/images/notification.png')); ?>"
                                                        class='rounded-pill w-100 h-100 object-fit-contain' alt="profile">
                                                </div>
                                                <div class="card-body p-0">
                                                    <div class="noti-heading pt-5 d-flex justify-content-start gap-2">
                                                        <p class="fs-13 semi_bold light-black m-0 "><?php echo e($notification->title ?? ''); ?></p>
                                                        <p class="fs-13 normal light-black opacity-8 m-0"> <?php echo e($notification->message ?? ''); ?></p>
                                                    </div>
                                                    <div class="mail-massage">
                                                        <p class="fs-14 normal dark-cool-gray line-clamp-1"><?php echo e($notification->created_at->diffForHumans()); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <div class="col-lg-12">
                                            <div class="d-flex flex-column align-items-center justify-content-center py-5 my-5">
                                                <i class="fas fa-bell-slash fa-4x text-muted mb-4"></i>
                                                <h4 class="text-muted mb-2">No Notifications Found</h4>
                                                <p class="text-muted fs-14 text-center">You don't have any notifications at the moment.<br>New notifications will appear here when you receive them.</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/notification.blade.php ENDPATH**/ ?>