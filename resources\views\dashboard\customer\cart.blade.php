@extends('website.layout.master')
@push('css')
<style>
    #couponError {
        font-size: 14px;
        margin-top: 5px;
    }
    #couponSuccess {
        font-size: 14px;
        margin-top: 5px;
    }
    .form-control.is-invalid {
        border-color: #dc3545;
    }
    .form-control.is-valid {
        border-color: #28a745;
    }
    #applyCouponBtn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
</style>
@endpush

@section('content')
    <div class="app-content flex-column-fluid customer_dashboard addfamily cart-section padding-block position-relative">
        <div class="app-container container padding-block h-100">
            <div class="row align-items-center justify-content-center h-100">
                <div class="col-md-12">
                    <div class="container-box">
                        <!-- Step 1: Cart -->
                        {{-- <div class="step active" id="step1"> --}}
                            @include('dashboard.templates.cart-stepper.cart-step')
                        {{-- </div> --}}
                        <!-- Step 2: Payment -->
                        {{-- <div class="step" id="step2">
                            @include('dashboard.templates.cart-stepper.payment-step')
                        </div>
                        <!-- Step 3: Booking Confirmation -->
                        <div class="step" id="step3">
                            @include('dashboard.templates.cart-stepper.booking-confirmation')
                        </div> --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        // Handle cart item deletion
        $(document).on('click', '.delete-cart-item', function() {
            const serviceId = $(this).data('service-id');
            const bookingDate = $(this).data('booking-date');
            const bookingTime = $(this).data('booking-time');
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to remove this item from your cart?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    deleteCartItem(serviceId, bookingDate, bookingTime, $(this));
                }
            });
        });
        // Function to delete cart item from session
        function deleteCartItem(serviceId, bookingDate, bookingTime, buttonElement) {
            $.ajax({
                url: "{{ route('remove_cart_item') }}",
                method: 'POST',
                data: {
                    service_id: serviceId,
                    booking_date: bookingDate,
                    booking_time: bookingTime,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        // Remove the cart item from the DOM
                        $('.cart-item[data-service-id="' + serviceId + '"]').fadeOut(300, function() {
                            $(this).remove();
                            updateCartCount();
                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: 'Item removed from cart successfully'
                            });

                            // Check if cart is empty and show appropriate message
                            if ($('.cart-item').length === 0) {
                                $('.cart-container').html(
                                    '<div class="empty-cart-message text-center py-4"><h5>Your cart is empty</h5><p>Add some services to get started!</p></div>'
                                );
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: response.message || 'Failed to remove item from cart'
                        });
                    }
                },
                error: function() {
                    showToast('error', 'An error occurred while removing the item');
                }
            });
        }

        // Function to update cart count
        function updateCartCount() {
            const cartCount = $('.cart-item').length;
            $('.cart-count').text(cartCount);

            // Update any cart badges or counters
            if (cartCount === 0) {
                $('.cart-badge').hide();
            }
        }

        // Coupon validation functionality
        $(document).on('click', '#applyCouponBtn', function() {
            const couponCode = $('#coupon').val().trim();

            if (!couponCode) {
                showCouponError('Please enter a coupon code');
                return;
            }

            // Show loading state
            const btn = $(this);
            const originalText = btn.text();
            btn.text('Applying...').prop('disabled', true);

            // Clear previous messages
            hideCouponMessages();

            // AJAX request to validate coupon
            $.ajax({
                url: "{{ route('validate_coupon') }}",
                method: 'POST',
                data: {
                    coupon_code: couponCode,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        showCouponSuccess(response.message || 'Coupon applied successfully!');
                        // Update cart totals if discount is provided
                        if (response.discount) {
                            updateCartTotals(response.discount, response.new_total);
                        }
                    } else {
                        showCouponError(response.message || 'Invalid coupon code');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'An error occurred while validating the coupon';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showCouponError(errorMessage);
                },
                complete: function() {
                    // Reset button state
                    btn.text(originalText).prop('disabled', false);
                }
            });
        });

        // Function to show coupon error
        function showCouponError(message) {
            $('#couponError').text(message).show();
            $('#couponSuccess').hide();
            $('#coupon').addClass('is-invalid');
        }

        // Function to show coupon success
        function showCouponSuccess(message) {
            $('#couponSuccess').text(message).show();
            $('#couponError').hide();
            $('#coupon').removeClass('is-invalid').addClass('is-valid');
        }

        // Function to hide coupon messages
        function hideCouponMessages() {
            $('#couponError, #couponSuccess').hide();
            $('#coupon').removeClass('is-invalid is-valid');
        }

        // Function to update cart totals after coupon application
        function updateCartTotals(discount, newTotal) {
            // Add discount row if it doesn't exist
            if ($('.discount-row').length === 0) {
                const discountRow = `
                    <div class="d-flex justify-content-between discount-row">
                        <p class="card-title fs-16 black regular">Discount</p>
                        <p class="fs-16 semi_bold m-0 text-success">-$${discount.toFixed(2)}</p>
                    </div>
                `;
                $('.cart-total').parent().before(discountRow);
            } else {
                $('.discount-row p:last-child').text('-$' + discount.toFixed(2));
            }

            // Update total
            $('.cart-total').text('$' + newTotal.toFixed(2));
        }

        // Clear coupon messages when user starts typing
        $(document).on('input', '#coupon', function() {
            if ($(this).val().trim() === '') {
                hideCouponMessages();
            }
        });

        $(document).ready(function() {
            function showStep(stepId) {
                $('.step').removeClass('active');
                $(stepId).addClass('active');
            }

            $('#goToPaymentStep').on('click', function() {
                showStep('#step2');
            });

            $('#goToConfirmation').on('click', function() {
                showStep('#step3');
            });

            $('.back-arrow').on('click', function() {
                let prevStep = $(this).data('prev');
                showStep(prevStep);
            });

        });
        $(document).on('click', '.previous-box .fa-xmark', function() {
            window.location.href = "{{ url('/') }}";
            $('#goToConfirmation').hide();

            $('#terms-condition').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#goToConfirmation').show();
                } else {
                    $('#goToConfirmation').hide();
                }
            });
        });
    </script>
@endpush
