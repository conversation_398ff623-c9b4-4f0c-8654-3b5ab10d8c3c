<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container family padding-block">
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="sora black">Friends</h4>
                        <a href="<?php echo e(route('friends.create')); ?>" class="add-btn"><i class="fa-solid fa-plus me-3"></i>Add a
                            Friend/Family</a>
                    </div>
                </div>

                <div class="col-md-12 family-tabs">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active service-tab" id="my-friends-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-my-friends" type="button" role="tab"
                                aria-controls="pills-my-friends" aria-selected="true">My Friends
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link service-tab" id="friend-requests-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-friend-requests" type="button" role="tab"
                                aria-controls="pills-friend-requests" aria-selected="true">Friend
                                Requests
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-my-friends" role="tabpanel"
                            aria-labelledby="my-friends-tab" tabindex="0">
                            <div class="blue-box">
                                <div class="row row-gap-5">
                                    <div class="col-md-12 d-flex gap-4">
                                        <?php echo $__env->make('svg.family', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div>
                                            <h4 class="sora black">Your Family Members/Friends</h4>
                                            <p class="light-black sora fs-14">Lorem ipsum dolor sit amet
                                                consectetur. Duis arcu sapien justo nulla.</p>
                                        </div>
                                    </div>
                                    <?php $__empty_1 = true; $__currentLoopData = $friends; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $friend): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <div class="col-md-3">
                                            <div class="card friends-cards bg-white h-100">
                                                <div class="card-header p-4 align-items-center">
                                                    <div class="d-flex justify-content-start gap-3 align-items-center">
                                                        <?php if($friend->type == 'under-13'): ?>
                                                            
                                                            <img src="<?php echo e(asset('website') . '/' . $friend->profile_pic); ?>"
                                                                class="h-60px w-60px object-fit-cover rounded-pill top-rated-image"
                                                                alt="<?php echo e($friend->name); ?>">
                                                        <?php else: ?>
                                                            
                                                            <img src="<?php echo e(asset('website' . '/' . $friend->friendUser->profile->pic ?? 'assets/images/family1.png')); ?>"
                                                                class="h-60px w-60px object-fit-cover rounded-pill top-rated-image"
                                                                alt="<?php echo e($friend->name); ?>">
                                                        <?php endif; ?>
                                                        <div>
                                                            <p class="sora black fs-16 m-0 semi_bold"><?php echo e($friend->name); ?>

                                                            </p>
                                                            <p class="light-black fs-14 opacity-6 m-0">
                                                                <?php echo e($friend->relationship); ?></p>
                                                            <span
                                                                class="badge badge-sm <?php echo e($friend->type == 'under-13' ? 'badge-primary' : 'badge-success'); ?>">
                                                                <?php echo e($friend->type == 'under-13' ? 'Under 13' : '13+ User'); ?>

                                                            </span>
                                                        </div>
                                                    </div>
                                                    <form action="<?php echo e(route('friends.destroy', $friend->ids)); ?>"
                                                        method="POST" class="d-inline delete-form">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="button"
                                                            class="drop-btn delete-btn btn btn-outline-danger py-2 px-3 text-center"
                                                            onclick="showDeleteConfirmation(this)" aria-label="Delete">
                                                            <i class="bi bi-trash p-0 red" aria-hidden="true"></i>
                                                            <span class="visually-hidden">Delete</span>
                                                        </button>
                                                    </form>
                                                </div>
                                                <div class="card-body p-4">
                                                    <?php if($friend->type == 'above-13' && $friend->friendUser): ?>
                                                        
                                                        <div class="d-flex flex-column gap-3">
                                                            <span class="fs-12 normal sora light-black">
                                                                <span class="me-3"><?php echo $__env->make('svg.building', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                                                <?php echo e($friend->friendUser->email); ?>

                                                            </span>
                                                            <?php if($friend->friendUser->phone): ?>
                                                                <span class="fs-12 normal sora light-black">
                                                                    <i class="fa-solid fa-phone me-3"></i>
                                                                    <?php echo e($friend->friendUser->phone); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php else: ?>
                                                        
                                                        <?php if($friend->service_preferences): ?>
                                                            <?php
                                                                $services = json_decode(
                                                                    $friend->service_preferences,
                                                                    true,
                                                                );
                                                            ?>
                                                            <div class="d-flex flex-column gap-2">
                                                                <small class="text-muted">Service Preferences:</small>
                                                                <div class="d-flex flex-wrap gap-1">
                                                                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $serviceId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php
                                                                            $service = \App\Models\Service::find(
                                                                                $serviceId,
                                                                            );
                                                                        ?>
                                                                        <?php if($service): ?>
                                                                            <span
                                                                                class="badge badge-light"><?php echo e($service->name); ?></span>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <?php if($friend->type == 'above-13' && $friend->friendUser->socials): ?>
                                                        <div class="d-flex gap-4 mt-5">
                                                            <?php $__currentLoopData = $friend->friendUser->socials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <a href="<?php echo e($social->link); ?>" target="_blank"
                                                                    class="logo-box">
                                                                    <img src="<?php echo e(asset('website') . '/' . $social->image); ?>"
                                                                        alt="social-logo" height="30px" width="30px">
                                                                </a>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="card-footer d-flex justify-content-between p-4">
                                                    <a href="<?php echo e(route('friends.show', $friend->ids)); ?>"
                                                        class="button1 px-xxl-17">View Profile</a>
                                                    <a href="<?php echo e(route('friends.edit', $friend->ids)); ?>"
                                                        class="gray-btn">Edit</a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <div class="col-md-12 text-center py-5">
                                            <div class="text-muted">
                                                <i class="fa-solid fa-users fa-3x mb-3"></i>
                                                <h5>No Friends Yet</h5>
                                                <p>Start by adding your first friend or family member!</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-friend-requests" role="tabpanel"
                            aria-labelledby="friend-requests-tab" tabindex="0">
                            <div class="blue-box">
                                <div class="row row-gap-5">
                                    <div class="col-md-12 d-flex gap-4">
                                        <?php echo $__env->make('svg.family', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <div>
                                            <h4 class="sora black">Friend Request: Accept/Reject</h4>
                                            <p class="light-black sora fs-14">Lorem ipsum dolor sit amet
                                                consectetur. Duis arcu sapien justo nulla.</p>
                                        </div>
                                    </div>
                                    <?php $__empty_1 = true; $__currentLoopData = $pending_friends; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pending_friend): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <div class="col-md-3">
                                            <div class="card friends-cards bg-white">
                                                <div
                                                    class="card-header justify-content-start gap-3 p-4 align-items-center">
                                                    
                                                    <img src="<?php echo e(asset('website') . '/' . $pending_friend->user->profile->pic ?? 'assets/images/family1.png'); ?>"
                                                        class="h-60px w-60px object-fit-cover rounded-pill top-rated-image"
                                                        alt="<?php echo e($pending_friend->user ? $pending_friend->user->name : 'User'); ?>">
                                                    <div>
                                                        <p class="sora black fs-16 m-0 semi_bold">
                                                            <?php echo e($pending_friend->user ? $pending_friend->user->name : 'Unknown User'); ?>

                                                        </p>
                                                        <p class="light-black fs-14 opacity-6 m-0">
                                                            Wants to be your <?php echo e($pending_friend->relationship); ?></p>
                                                        <span class="badge badge-warning">Pending Request</span>
                                                        <span class="badge badge-sm badge-success">13+ User</span>
                                                    </div>
                                                </div>
                                                <div class="card-body p-4">
                                                    
                                                    <?php if($pending_friend->user): ?>
                                                        <div class="d-flex flex-column gap-3">
                                                            <span class="fs-12 normal sora light-black">
                                                                <span class="me-3"><?php echo $__env->make('svg.building', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                                                <?php echo e($pending_friend->user->email); ?>

                                                            </span>
                                                            <?php if($pending_friend->user->phone): ?>
                                                                <span class="fs-12 normal sora light-black">
                                                                    <i class="fa-solid fa-phone me-3"></i>
                                                                    <?php echo e($pending_friend->user->phone); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="card-footer d-flex justify-content-between p-4">
                                                    <form action="<?php echo e(route('friends.accept', $pending_friend->ids)); ?>"
                                                        method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PATCH'); ?>
                                                        <button type="submit" class="button1 px-xxl-17">Accept</button>
                                                    </form>
                                                    <form action="<?php echo e(route('friends.reject', $pending_friend->ids)); ?>"
                                                        method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PATCH'); ?>
                                                        <button type="submit" class="gray-btn"
                                                            onclick="return confirm('Are you sure you want to reject this friend request?')">Reject</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <div class="col-md-12 text-center py-5">
                                            <div class="text-muted">
                                                <i class="fa-solid fa-clock fa-3x mb-3"></i>
                                                <h5>No Pending Requests</h5>
                                                <p>You don't have any pending friend requests at the moment.</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/website/friends/index.blade.php ENDPATH**/ ?>