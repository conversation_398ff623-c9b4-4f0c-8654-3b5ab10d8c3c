
<div>
    <ul class="nav nav-pills  service-subcategorymb-10" id="pills-tab" role="tablist">
        <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?php echo e($loop->first ? 'active' : ''); ?> service-tab subcategory-tab-btn"
                    id="pills-fitness-tab" data-bs-toggle="pill" data-bs-target="#pills-fitness" type="button"
                    role="tab" data-category-name="<?php echo e($subcategory->category->slug); ?>"
                    data-subcategory-name="<?php echo e($subcategory->slug); ?>" aria-controls="pills-fitness" aria-selected="true"
                    href="#services-personal-trainer-subcategory-1"><?php echo e($subcategory->name); ?>

                </button>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
    
</div>

<div class="tab-content mt-10" id="pills-tabContent  ">
    <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab"
        tabindex="0">
        <div class="tab-content mt-10" id="pills-tabContent  ">
            <div class="tab-pane fade active show" id="pills-fitness" role="tabpanel"
                aria-labelledby="pills-fitness-tab" tabindex="0">
                <div class="row row-gap-8">
                    <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                        <?php echo $__env->make('website.template.top-rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endif; ?>
                    <?php $__empty_1 = true; $__currentLoopData = $professionals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $professional): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <a href="<?php echo e(route("professional_profile", $professional->ids)); ?>">
                                <div class="card top-rated-card">
                                    <div class="card-header border-0 p-0 position-relative">
                                        <div class="top-rated-image">
                                            <img src="<?php echo e(asset('website') . '/' . $professional->profile->pic ?? ''); ?>"
                                                class="w-100 h-100 img-fluid" alt="card-image">
                                        </div>
                                        <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                                            <div class="fav-icon position-absolute  bottom-10 ">
                                                <?php if(auth()->user()->isFavorited($professional->id)): ?>
                                                    <i class="fa-solid fa-heart" style="color: red;"></i>
                                                <?php else: ?>
                                                    <i class="fa-regular fa-heart"></i>
                                                <?php endif; ?>
                                                <input type="hidden" name="wishlist_product_id" value="123">
                                            </div>
                                        <?php endif; ?>
                                        <div class="rated-div position-absolute">
                                            <p class="fs-12 sora semi_bold m-0">
                                                <?php echo $__env->make('svg.rated', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>TOP RATED
                                            </p>
                                        </div>
                                    </div>
                                    <div class="card-body pb-0 p-5">
                                        <p class="fs-16 semi_bold black m-0 "><?php echo e($professional->name ?? ''); ?></p>
                                        <p class="fs-15 sora bold m-0 light-black">4.5
                                            <i class="fa-solid fa-star review-icon mx-1"></i>
                                            <span class="normal">(440)</span>
                                        </p>
                                        <?php if($professional->profile->country): ?>
                                            <p class="fs-14 regular light-black">
                                                <?php echo e($professional->profile->city ?? ''); ?>,
                                                <?php echo e($professional->profile->country ?? ''); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-footer border-0 pt-0 p-5">
                                        <?php $__currentLoopData = $professional->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $professionalCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge white-badge"><?php echo e($professionalCategory->name); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="fs-14 sora light-black normal service-details">No Professionals Found</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/website/template/subcategory-professional.blade.php ENDPATH**/ ?>