<table id="responsiveTable" class="display nowrap w-100">
    <thead>
        <tr>
            <th>Booking ID</th>
            <th>Customer Name</th>
            <th>Service Name</th>
            <th>Service Type</th>
            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                <th>Provider</th>
            <?php endif; ?>
            <th>Status</th>
            <th>Date & Time</th>
            <th>Amount</th>
            <th></th>
        </tr>
    </thead>
    <tbody id="bookingTableBody">
        <?php echo $__env->make('dashboard.business.partials.booking-table-rows', ['bookings' => $bookings], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </tbody>
</table>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/partials/booking-table.blade.php ENDPATH**/ ?>