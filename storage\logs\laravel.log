[2025-08-05 10:27:26] local.INFO: Broadcasting message to conversation: 1 {"id":44,"content":"hello","message_type":"text","attachments":[],"sender":{"id":51,"name":"<PERSON>","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:27:26","formatted_time":"10:27","conversation_id":1} 
[2025-08-05 10:27:28] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 10:27:28] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 10:27:28] local.INFO: 🔔 BACKEND: Message data:  {"id":44,"content":"hello","message_type":"text","attachments":[],"sender":{"id":51,"name":"<PERSON>","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:27:26","formatted_time":"10:27","conversation_id":1} 
[2025-08-05 10:27:28] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 10:37:44] local.INFO: Broadcasting message to conversation: 1 {"id":45,"content":"fsdsd","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:37:44","formatted_time":"10:37","conversation_id":1} 
[2025-08-05 10:37:45] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 10:37:45] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 10:37:45] local.INFO: 🔔 BACKEND: Message data:  {"id":45,"content":"fsdsd","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:37:44","formatted_time":"10:37","conversation_id":1} 
[2025-08-05 10:37:45] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 10:39:58] local.INFO: Broadcasting message to conversation: 1 {"id":46,"content":"gdfgdfg","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:39:58","formatted_time":"10:39","conversation_id":1} 
[2025-08-05 10:40:00] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 10:40:00] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 10:40:00] local.INFO: 🔔 BACKEND: Message data:  {"id":46,"content":"gdfgdfg","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:39:58","formatted_time":"10:39","conversation_id":1} 
[2025-08-05 10:40:00] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 10:55:23] local.INFO: Broadcasting message to conversation: 1 {"id":47,"content":"dcdc","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:55:23","formatted_time":"10:55","conversation_id":1} 
[2025-08-05 10:55:24] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 10:55:24] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 10:55:24] local.INFO: 🔔 BACKEND: Message data:  {"id":47,"content":"dcdc","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 10:55:23","formatted_time":"10:55","conversation_id":1} 
[2025-08-05 10:55:24] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 10:59:00] local.ERROR: syntax error, unexpected end of file, expecting "elseif" or "else" or "endif" {"view":{"view":"D:\\SALMAN\\git\\anders\\resources\\views\\website\\layout\\master.blade.php","data":{"crud":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1228</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Crud
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Crud</span> {<a class=sf-dump-ref>#1230</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">cruds</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Crud
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Crud</span> {<a class=sf-dump-ref>#1231</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">cruds</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Models\\Crud
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Crud</span> {<a class=sf-dump-ref>#1232</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">cruds</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Models\\Crud
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Crud</span> {<a class=sf-dump-ref>#1233</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">cruds</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","pages":"<pre class=sf-dump id=sf-dump-872073511 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1553</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\CmsPage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CmsPage</span> {<a class=sf-dump-ref>#1555</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">cms_pages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-872073511\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errors":"<pre class=sf-dump id=sf-dump-974250772 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1572</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-974250772\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page":"<pre class=sf-dump id=sf-dump-344764392 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Home</span> {<a class=sf-dump-ref>#1605</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">homes</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>banner_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/L4RQZoRjxElvr6TzGDRfrihpe7eANmBZaQ0xqtqe.png</span>\"
    \"<span class=sf-dump-key>kicker</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Welcome to Stylenest</span>\"
    \"<span class=sf-dump-key>heading_one</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Find the best Professionals</span>\"
    \"<span class=sf-dump-key>heading_two</span>\" => \"<span class=sf-dump-str title=\"24 characters\">for Your Needs. Anytime!</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Lorem ipsum dolor sit amet consectetur. Ut et id laoreet.</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/yfo0MfYkYjAHEgaqzqsqoe1EXwclgs6MxB1zYv3P.png</span>\"
    \"<span class=sf-dump-key>section_two_bg_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/xfh9jR7wAu6uiVAsTmGIR6ix8DifzwyhYr9UddyW.png</span>\"
    \"<span class=sf-dump-key>section_two_title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Who we are</span>\"
    \"<span class=sf-dump-key>section_two_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/3w3TYiIWstTOYEe16b9GGELFm5qts7LQz5kSzUOt.png</span>\"
    \"<span class=sf-dump-key>section_two_heading</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Lorem ipsum dolor sit amet consectetur. Nunc.</span>\"
    \"<span class=sf-dump-key>section_two_description</span>\" => \"<span class=sf-dump-str title=\"185 characters\">Lorem ipsum dolor sit amet consectetur. Augue ut tempus scelerisque sollicitudin elementum. Blandit tellus risus consequat cras eu integer molestie. In imperdiet tincidunt aliquet quam.</span>\"
    \"<span class=sf-dump-key>section_three_title</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Signup as a business</span>\"
    \"<span class=sf-dump-key>section_three_bg_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/pTXcqqcIYq5ANPNazzwSp7SL3PSXjofN6ZbnVwbZ.png</span>\"
    \"<span class=sf-dump-key>section_three_heading</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Partner with us</span>\"
    \"<span class=sf-dump-key>section_three_button_one_text</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Become a professional</span>\"
    \"<span class=sf-dump-key>section_three_button_one_link</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>section_three_button_two_text</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Learn More</span>\"
    \"<span class=sf-dump-key>section_three_button_two_link</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-25 14:23:30</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>banner_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/L4RQZoRjxElvr6TzGDRfrihpe7eANmBZaQ0xqtqe.png</span>\"
    \"<span class=sf-dump-key>kicker</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Welcome to Stylenest</span>\"
    \"<span class=sf-dump-key>heading_one</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Find the best Professionals</span>\"
    \"<span class=sf-dump-key>heading_two</span>\" => \"<span class=sf-dump-str title=\"24 characters\">for Your Needs. Anytime!</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Lorem ipsum dolor sit amet consectetur. Ut et id laoreet.</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/yfo0MfYkYjAHEgaqzqsqoe1EXwclgs6MxB1zYv3P.png</span>\"
    \"<span class=sf-dump-key>section_two_bg_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/xfh9jR7wAu6uiVAsTmGIR6ix8DifzwyhYr9UddyW.png</span>\"
    \"<span class=sf-dump-key>section_two_title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Who we are</span>\"
    \"<span class=sf-dump-key>section_two_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/3w3TYiIWstTOYEe16b9GGELFm5qts7LQz5kSzUOt.png</span>\"
    \"<span class=sf-dump-key>section_two_heading</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Lorem ipsum dolor sit amet consectetur. Nunc.</span>\"
    \"<span class=sf-dump-key>section_two_description</span>\" => \"<span class=sf-dump-str title=\"185 characters\">Lorem ipsum dolor sit amet consectetur. Augue ut tempus scelerisque sollicitudin elementum. Blandit tellus risus consequat cras eu integer molestie. In imperdiet tincidunt aliquet quam.</span>\"
    \"<span class=sf-dump-key>section_three_title</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Signup as a business</span>\"
    \"<span class=sf-dump-key>section_three_bg_image</span>\" => \"<span class=sf-dump-str title=\"56 characters\">home-images/pTXcqqcIYq5ANPNazzwSp7SL3PSXjofN6ZbnVwbZ.png</span>\"
    \"<span class=sf-dump-key>section_three_heading</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Partner with us</span>\"
    \"<span class=sf-dump-key>section_three_button_one_text</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Become a professional</span>\"
    \"<span class=sf-dump-key>section_three_button_one_link</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>section_three_button_two_text</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Learn More</span>\"
    \"<span class=sf-dump-key>section_three_button_two_link</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-25 14:23:30</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>details</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1609</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">banner_image</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">kicker</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">heading_one</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">heading_two</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"20 characters\">section_two_bg_image</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"17 characters\">section_two_title</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"17 characters\">section_two_image</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">section_two_heading</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"23 characters\">section_two_description</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"19 characters\">section_three_title</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"22 characters\">section_three_bg_image</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"21 characters\">section_three_heading</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"29 characters\">section_three_button_one_text</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"29 characters\">section_three_button_one_link</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"29 characters\">section_three_button_two_text</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"29 characters\">section_three_button_two_link</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-344764392\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-237010081 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1610</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Category
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Category</span> {<a class=sf-dump-ref>#1617</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Category
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Category</span> {<a class=sf-dump-ref>#1618</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Models\\Category
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Category</span> {<a class=sf-dump-ref>#1619</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-237010081\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","professionals":"<pre class=sf-dump id=sf-dump-1489918100 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1632</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>User</span> {<a class=sf-dump-ref>#1636</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>User</span> {<a class=sf-dump-ref>#1634</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>User</span> {<a class=sf-dump-ref>#1635</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>User</span> {<a class=sf-dump-ref>#1638</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
    </samp>}
    <span class=sf-dump-index>4</span> => <span class=sf-dump-note title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>User</span> {<a class=sf-dump-ref>#1639</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1489918100\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","words":"<pre class=sf-dump id=sf-dump-1147140607 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">Find</span>\"
  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">the</span>\"
  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">best</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1147140607\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","lastWord":"<pre class=sf-dump id=sf-dump-1940758222 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"13 characters\">Professionals</span>\"
</pre><script>Sfdump(\"sf-dump-1940758222\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","remainingWords":"<pre class=sf-dump id=sf-dump-1041415201 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"13 characters\">Find the best</span>\"
</pre><script>Sfdump(\"sf-dump-1041415201\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-590564610 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#1609</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\HomeDetail
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HomeDetail</span> {<a class=sf-dump-ref>#1616</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"12 characters\">home_details</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\HomeDetail
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HomeDetail</span> {<a class=sf-dump-ref>#1614</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"12 characters\">home_details</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-590564610\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","category":"<pre class=sf-dump id=sf-dump-34723169 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Category</span> {<a class=sf-dump-ref>#1619</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>ids</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d2afaf43-f933-4a57-8961-08b1c7a29647</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"60 characters\">category-images/DiiVBx5ofGnOFyY3RWOKP7AztT0fPKZrhEEPuNVH.gif</span>\"
    \"<span class=sf-dump-key>alt_tag</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Do eu inventore nost</span>\"
    \"<span class=sf-dump-key>image_description</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptatum nulla eos</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Alyssa Cox</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Excepturi irure quod</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"10 characters\">alyssa-cox</span>\"
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:47:36</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:47:36</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>ids</span>\" => \"<span class=sf-dump-str title=\"36 characters\">d2afaf43-f933-4a57-8961-08b1c7a29647</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"60 characters\">category-images/DiiVBx5ofGnOFyY3RWOKP7AztT0fPKZrhEEPuNVH.gif</span>\"
    \"<span class=sf-dump-key>alt_tag</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Do eu inventore nost</span>\"
    \"<span class=sf-dump-key>image_description</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Voluptatum nulla eos</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Alyssa Cox</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Excepturi irure quod</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"10 characters\">alyssa-cox</span>\"
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:47:36</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-01 12:47:36</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">alt_tag</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">image_description</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-34723169\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1938299767 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1938299767\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","professional":"<pre class=sf-dump id=sf-dump-1845058700 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\User</span> {<a class=sf-dump-ref>#1639</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>51</span>
    \"<span class=sf-dump-key>ids</span>\" => \"<span class=sf-dump-str title=\"36 characters\">489a475b-959a-4f75-8335-c028789a4c12</span>\"
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Damian Leach</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$.fuU0W69t0hPtG7GDxbYQ.hgWTPOX0MKceCcwKQKMJOCZkg9M7sGu</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 15:13:37</span>\"
    \"<span class=sf-dump-key>provider_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>provider</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>registration_completed</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>approval</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>stripe_account_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_enabled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_online</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>online_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 09:27:32</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 15:13:37</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 09:27:32</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>51</span>
    \"<span class=sf-dump-key>ids</span>\" => \"<span class=sf-dump-str title=\"36 characters\">489a475b-959a-4f75-8335-c028789a4c12</span>\"
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Damian Leach</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"21 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$.fuU0W69t0hPtG7GDxbYQ.hgWTPOX0MKceCcwKQKMJOCZkg9M7sGu</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 15:13:37</span>\"
    \"<span class=sf-dump-key>provider_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>provider</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>registration_completed</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>approval</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>stripe_account_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_enabled</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_online</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>online_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 09:27:32</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 15:13:37</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 09:27:32</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>online_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>is_online</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>profile</span>\" => <span class=sf-dump-note title=\"App\\Models\\Profile
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Profile</span> {<a class=sf-dump-ref>#1664</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">profiles</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:28</span> [ &#8230;28]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:28</span> [ &#8230;28]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:37</span> [ &#8230;37]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">short_url</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">is_online</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">online_at</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1845058700\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","images":"<pre class=sf-dump id=sf-dump-134420019 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">family1.png</span>\"
  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">family2.png</span>\"
  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">family3.png</span>\"
  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">family4.png</span>\"
  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">family4.png</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-134420019\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","i":"<pre class=sf-dump id=sf-dump-544794662 data-indent-pad=\"  \"><span class=sf-dump-num>4</span>
</pre><script>Sfdump(\"sf-dump-544794662\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","detail":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\HomeDetail</span> {<a class=sf-dump-ref>#1614</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"12 characters\">home_details</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>home_id</span>\" => \"<span class=sf-dump-str>1</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"57 characters\">home-details/FemvNINDz7BfHdb8eiwyPM3kOh9w339jQf67KXx4.png</span>\"
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Consumers</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"98 characters\">Auctor elementum etiam congue gravida posuere nostra inceptos scelerisque mus consequat imperdiet.</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-25 14:23:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-25 14:23:30</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>home_id</span>\" => \"<span class=sf-dump-str>1</span>\"
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"57 characters\">home-details/FemvNINDz7BfHdb8eiwyPM3kOh9w339jQf67KXx4.png</span>\"
    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Consumers</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"98 characters\">Auctor elementum etiam congue gravida posuere nostra inceptos scelerisque mus consequat imperdiet.</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-25 14:23:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-25 14:23:30</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">page_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":3,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): syntax error, unexpected end of file, expecting \"elseif\" or \"else\" or \"endif\" at D:\\SALMAN\\git\\anders\\resources\\views\\website\\layout\\master.blade.php:520)
[stacktrace]
#0 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(125): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\SALMAN\\\\git\\\\a...', Array)
#2 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\SALMAN\\\\git\\\\a...', Array)
#3 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\SALMAN\\\\git\\\\a...', Array)
#4 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#5 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#6 D:\\SALMAN\\git\\anders\\resources\\views\\website\\index.blade.php(512): Illuminate\\View\\View->render()
#7 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): require('D:\\\\SALMAN\\\\git\\\\a...')
#8 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(125): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\SALMAN\\\\git\\\\a...', Array)
#10 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\SALMAN\\\\git\\\\a...', Array)
#11 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\SALMAN\\\\git\\\\a...', Array)
#12 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#13 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#14 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#15 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(911): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(878): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\UserCheckMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserCheckMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\HtmlMinifier.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\HtmlMinifier->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 D:\\SALMAN\\git\\anders\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\SALMAN\\\\git\\\\a...')
#69 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected end of file, expecting \"elseif\" or \"else\" or \"endif\" at D:\\SALMAN\\git\\anders\\storage\\framework\\views\\b0dd843f6ce12d675580bf282a51122a.php:520)
[stacktrace]
#0 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(125): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\SALMAN\\\\git\\\\a...', Array)
#2 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\SALMAN\\\\git\\\\a...', Array)
#3 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\SALMAN\\\\git\\\\a...', Array)
#4 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#5 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#6 D:\\SALMAN\\git\\anders\\storage\\framework\\views\\41491f7b72b8454fa736817225740664.php(518): Illuminate\\View\\View->render()
#7 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): require('D:\\\\SALMAN\\\\git\\\\a...')
#8 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(125): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\SALMAN\\\\git\\\\a...', Array)
#10 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\SALMAN\\\\git\\\\a...', Array)
#11 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\SALMAN\\\\git\\\\a...', Array)
#12 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(178): Illuminate\\View\\View->getContents()
#13 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Illuminate\\View\\View->renderContents()
#14 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#15 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(911): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(878): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\UserCheckMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\UserCheckMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\HtmlMinifier.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\HtmlMinifier->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 D:\\SALMAN\\git\\anders\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\SALMAN\\\\git\\\\a...')
#69 {main}
"} 
[2025-08-05 12:27:17] local.INFO: Broadcasting message to conversation: 1 {"id":48,"content":"hello","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:27:17","formatted_time":"12:27","conversation_id":1} 
[2025-08-05 12:27:21] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 12:27:21] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 12:27:21] local.INFO: 🔔 BACKEND: Message data:  {"id":48,"content":"hello","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:27:17","formatted_time":"12:27","conversation_id":1} 
[2025-08-05 12:27:21] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:27:55] local.INFO: Broadcasting message to conversation: 1 {"id":49,"content":"rgrege","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:27:55","formatted_time":"12:27","conversation_id":1} 
[2025-08-05 12:27:56] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 12:27:56] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 12:27:56] local.INFO: 🔔 BACKEND: Message data:  {"id":49,"content":"rgrege","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:27:55","formatted_time":"12:27","conversation_id":1} 
[2025-08-05 12:27:56] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:28:10] local.INFO: Broadcasting message to conversation: 1 {"id":50,"content":"gtgtr","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:28:10","formatted_time":"12:28","conversation_id":1} 
[2025-08-05 12:28:11] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 12:28:11] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 12:28:11] local.INFO: 🔔 BACKEND: Message data:  {"id":50,"content":"gtgtr","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:28:10","formatted_time":"12:28","conversation_id":1} 
[2025-08-05 12:28:11] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:28:44] local.INFO: Broadcasting message to conversation: 1 {"id":51,"content":"vdfvdf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:28:44","formatted_time":"12:28","conversation_id":1} 
[2025-08-05 12:28:47] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 12:28:47] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 12:28:47] local.INFO: 🔔 BACKEND: Message data:  {"id":51,"content":"vdfvdf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:28:44","formatted_time":"12:28","conversation_id":1} 
[2025-08-05 12:28:47] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:29:18] local.INFO: Broadcasting message to conversation: 1 {"id":52,"content":"vfdv","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:29:18","formatted_time":"12:29","conversation_id":1} 
[2025-08-05 12:29:19] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 12:29:19] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 12:29:19] local.INFO: 🔔 BACKEND: Message data:  {"id":52,"content":"vfdv","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:29:18","formatted_time":"12:29","conversation_id":1} 
[2025-08-05 12:29:19] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:34:20] local.INFO: Broadcasting message to conversation: 1 {"id":53,"content":"vdvd","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:34:20","formatted_time":"12:34","conversation_id":1} 
[2025-08-05 12:34:21] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 12:34:21] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 12:34:21] local.INFO: 🔔 BACKEND: Message data:  {"id":53,"content":"vdvd","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:34:20","formatted_time":"12:34","conversation_id":1} 
[2025-08-05 12:34:21] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:39:23] local.INFO: Broadcasting message to conversation: 1 {"id":54,"content":"vdfvdfv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:39:23","formatted_time":"12:39","conversation_id":1} 
[2025-08-05 12:39:24] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 12:39:24] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 12:39:24] local.INFO: 🔔 BACKEND: Message data:  {"id":54,"content":"vdfvdfv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:39:23","formatted_time":"12:39","conversation_id":1} 
[2025-08-05 12:39:24] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:39:33] local.INFO: Broadcasting message to conversation: 1 {"id":55,"content":"🧐","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:39:33","formatted_time":"12:39","conversation_id":1} 
[2025-08-05 12:39:34] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 12:39:34] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 12:39:34] local.INFO: 🔔 BACKEND: Message data:  {"id":55,"content":"🧐","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:39:33","formatted_time":"12:39","conversation_id":1} 
[2025-08-05 12:39:34] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:39:46] local.INFO: Broadcasting message to conversation: 1 {"id":56,"content":"vdv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:39:46","formatted_time":"12:39","conversation_id":1} 
[2025-08-05 12:39:47] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 12:39:47] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 12:39:47] local.INFO: 🔔 BACKEND: Message data:  {"id":56,"content":"vdv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:39:46","formatted_time":"12:39","conversation_id":1} 
[2025-08-05 12:39:47] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:48:18] local.INFO: Broadcasting message to conversation: 1 {"id":57,"content":"udhfud","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:48:18","formatted_time":"12:48","conversation_id":1} 
[2025-08-05 12:48:21] local.ERROR: Pusher error: cURL error 35: OpenSSL SSL_connect: Connection was reset in connection to api-ap2.pusher.com:443  (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api-ap2.pusher.com/apps/*******/events?auth_key=61adaf56059734aefbeb&auth_timestamp=1754398099&auth_version=1.0&body_md5=d02cf99b497f78056ab76ab5b2065608&auth_signature=07eb16ffa2b71b38306582ba0653e4c06e4ea69d64b88e1bb96284b49912aa96. {"userId":51,"exception":"[object] (Illuminate\\Broadcasting\\BroadcastException(code: 0): Pusher error: cURL error 35: OpenSSL SSL_connect: Connection was reset in connection to api-ap2.pusher.com:443  (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api-ap2.pusher.com/apps/*******/events?auth_key=61adaf56059734aefbeb&auth_timestamp=1754398099&auth_version=1.0&body_md5=d02cf99b497f78056ab76ab5b2065608&auth_signature=07eb16ffa2b71b38306582ba0653e4c06e4ea69d64b88e1bb96284b49912aa96. at D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster.php:164)
[stacktrace]
#0 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastEvent.php(92): Illuminate\\Broadcasting\\Broadcasters\\PusherBroadcaster->broadcast(Object(Illuminate\\Support\\Collection), 'message.sent', Array)
#1 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Broadcasting\\BroadcastEvent->handle(Object(Illuminate\\Broadcasting\\BroadcastManager))
#2 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#7 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(Illuminate\\Broadcasting\\BroadcastEvent))
#8 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Broadcasting\\BroadcastEvent))
#9 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(123): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Broadcasting\\BroadcastEvent), false)
#11 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(Illuminate\\Broadcasting\\BroadcastEvent))
#12 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Broadcasting\\BroadcastEvent))
#13 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(122): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\SyncJob), Object(Illuminate\\Broadcasting\\BroadcastEvent))
#15 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\SyncJob), Array)
#16 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SyncQueue.php(43): Illuminate\\Queue\\Jobs\\Job->fire()
#17 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php(57): Illuminate\\Queue\\SyncQueue->push(Object(Illuminate\\Broadcasting\\BroadcastEvent), '', NULL)
#18 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(183): Illuminate\\Queue\\Queue->pushOn(NULL, Object(Illuminate\\Broadcasting\\BroadcastEvent))
#19 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(320): Illuminate\\Broadcasting\\BroadcastManager->queue(Object(App\\Events\\MessageSent))
#20 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(243): Illuminate\\Events\\Dispatcher->broadcastEvent(Object(App\\Events\\MessageSent))
#21 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\PendingBroadcast.php(72): Illuminate\\Events\\Dispatcher->dispatch('App\\\\Events\\\\Mess...')
#22 D:\\SALMAN\\git\\anders\\app\\Http\\Controllers\\ChatController.php(226): Illuminate\\Broadcasting\\PendingBroadcast->__destruct()
#23 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ChatController->sendMessage(Object(Illuminate\\Http\\Request))
#24 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('sendMessage', Array)
#25 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ChatController), 'sendMessage')
#26 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#27 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#28 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\HtmlMinifier.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\HtmlMinifier->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\SALMAN\\git\\anders\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\SALMAN\\git\\anders\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\SALMAN\\git\\anders\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('D:\\\\SALMAN\\\\git\\\\a...')
#78 {main}
"} 
[2025-08-05 12:48:33] local.INFO: Broadcasting message to conversation: 1 {"id":58,"content":"fsf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:48:33","formatted_time":"12:48","conversation_id":1} 
[2025-08-05 12:48:34] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 12:48:34] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 12:48:34] local.INFO: 🔔 BACKEND: Message data:  {"id":58,"content":"fsf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:48:33","formatted_time":"12:48","conversation_id":1} 
[2025-08-05 12:48:34] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 12:50:48] local.INFO: Broadcasting message to conversation: 1 {"id":59,"content":"eeqw","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:50:48","formatted_time":"12:50","conversation_id":1} 
[2025-08-05 12:50:50] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 12:50:50] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 12:50:50] local.INFO: 🔔 BACKEND: Message data:  {"id":59,"content":"eeqw","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 12:50:48","formatted_time":"12:50","conversation_id":1} 
[2025-08-05 12:50:50] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:05:12] local.INFO: Broadcasting message to conversation: 1 {"id":60,"content":"fsdf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:05:12","formatted_time":"13:05","conversation_id":1} 
[2025-08-05 13:05:14] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 13:05:14] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 13:05:14] local.INFO: 🔔 BACKEND: Message data:  {"id":60,"content":"fsdf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:05:12","formatted_time":"13:05","conversation_id":1} 
[2025-08-05 13:05:14] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:05:28] local.INFO: Broadcasting message to conversation: 1 {"id":61,"content":"ferfe","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:05:28","formatted_time":"13:05","conversation_id":1} 
[2025-08-05 13:05:29] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 13:05:29] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 13:05:29] local.INFO: 🔔 BACKEND: Message data:  {"id":61,"content":"ferfe","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:05:28","formatted_time":"13:05","conversation_id":1} 
[2025-08-05 13:05:29] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:06:30] local.INFO: Broadcasting message to conversation: 1 {"id":62,"content":"fwf","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:06:30","formatted_time":"13:06","conversation_id":1} 
[2025-08-05 13:06:32] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 13:06:32] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 13:06:32] local.INFO: 🔔 BACKEND: Message data:  {"id":62,"content":"fwf","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:06:30","formatted_time":"13:06","conversation_id":1} 
[2025-08-05 13:06:32] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:10:15] local.INFO: Broadcasting message to conversation: 1 {"id":63,"content":"fvfsdv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:10:15","formatted_time":"13:10","conversation_id":1} 
[2025-08-05 13:10:17] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 13:10:17] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 13:10:17] local.INFO: 🔔 BACKEND: Message data:  {"id":63,"content":"fvfsdv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:10:15","formatted_time":"13:10","conversation_id":1} 
[2025-08-05 13:10:17] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:10:28] local.INFO: Broadcasting message to conversation: 1 {"id":64,"content":"sdcsd","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:10:28","formatted_time":"13:10","conversation_id":1} 
[2025-08-05 13:10:29] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 13:10:29] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 13:10:29] local.INFO: 🔔 BACKEND: Message data:  {"id":64,"content":"sdcsd","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:10:28","formatted_time":"13:10","conversation_id":1} 
[2025-08-05 13:10:29] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:53:57] local.INFO: Broadcasting message to conversation: 1 {"id":65,"content":"gg","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:53:57","formatted_time":"13:53","conversation_id":1} 
[2025-08-05 13:54:06] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 13:54:06] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 13:54:06] local.INFO: 🔔 BACKEND: Message data:  {"id":65,"content":"gg","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:53:57","formatted_time":"13:53","conversation_id":1} 
[2025-08-05 13:54:06] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 13:55:18] local.INFO: Broadcasting message to conversation: 1 {"id":66,"content":"vdfvdf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:55:18","formatted_time":"13:55","conversation_id":1} 
[2025-08-05 13:55:19] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 13:55:19] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 13:55:19] local.INFO: 🔔 BACKEND: Message data:  {"id":66,"content":"vdfvdf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 13:55:18","formatted_time":"13:55","conversation_id":1} 
[2025-08-05 13:55:19] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:17:02] local.INFO: Broadcasting message to conversation: 1 {"id":67,"content":"erfer","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:17:02","formatted_time":"14:17","conversation_id":1} 
[2025-08-05 14:17:03] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 14:17:03] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 14:17:03] local.INFO: 🔔 BACKEND: Message data:  {"id":67,"content":"erfer","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:17:02","formatted_time":"14:17","conversation_id":1} 
[2025-08-05 14:17:04] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:18:13] local.INFO: Broadcasting message to conversation: 1 {"id":68,"content":"rwerewr","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:18:12","formatted_time":"14:18","conversation_id":1} 
[2025-08-05 14:18:14] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 14:18:14] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 14:18:14] local.INFO: 🔔 BACKEND: Message data:  {"id":68,"content":"rwerewr","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:18:12","formatted_time":"14:18","conversation_id":1} 
[2025-08-05 14:18:14] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:19:33] local.INFO: Broadcasting message to conversation: 1 {"id":69,"content":"bfgbf","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:19:32","formatted_time":"14:19","conversation_id":1} 
[2025-08-05 14:19:33] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:19:33] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:19:33] local.INFO: 🔔 BACKEND: Message data:  {"id":69,"content":"bfgbf","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:19:32","formatted_time":"14:19","conversation_id":1} 
[2025-08-05 14:19:34] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:32:30] local.INFO: Broadcasting message to conversation: 1 {"id":70,"content":"vfvf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:32:30","formatted_time":"14:32","conversation_id":1} 
[2025-08-05 14:32:31] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 14:32:31] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 14:32:31] local.INFO: 🔔 BACKEND: Message data:  {"id":70,"content":"vfvf","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:32:30","formatted_time":"14:32","conversation_id":1} 
[2025-08-05 14:32:31] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:33:00] local.INFO: Broadcasting message to conversation: 1 {"id":71,"content":"fsdfsdf","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:33:00","formatted_time":"14:33","conversation_id":1} 
[2025-08-05 14:33:01] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:33:01] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:33:01] local.INFO: 🔔 BACKEND: Message data:  {"id":71,"content":"fsdfsdf","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:33:00","formatted_time":"14:33","conversation_id":1} 
[2025-08-05 14:33:01] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:39:20] local.INFO: Broadcasting message to conversation: 1 {"id":72,"content":"helffdvdfdv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:39:20","formatted_time":"14:39","conversation_id":1} 
[2025-08-05 14:39:21] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:39:21] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:39:21] local.INFO: 🔔 BACKEND: Message data:  {"id":72,"content":"helffdvdfdv","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:39:20","formatted_time":"14:39","conversation_id":1} 
[2025-08-05 14:39:21] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:46:27] local.INFO: Broadcasting message to conversation: 1 {"id":73,"content":"irjhfirofj","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:46:27","formatted_time":"14:46","conversation_id":1} 
[2025-08-05 14:46:29] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:46:29] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:46:29] local.INFO: 🔔 BACKEND: Message data:  {"id":73,"content":"irjhfirofj","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:46:27","formatted_time":"14:46","conversation_id":1} 
[2025-08-05 14:46:29] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:46:44] local.INFO: Broadcasting message to conversation: 1 {"id":74,"content":"fvfv","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:46:44","formatted_time":"14:46","conversation_id":1} 
[2025-08-05 14:46:45] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 14:46:45] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 14:46:45] local.INFO: 🔔 BACKEND: Message data:  {"id":74,"content":"fvfv","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:46:44","formatted_time":"14:46","conversation_id":1} 
[2025-08-05 14:46:45] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:47:08] local.INFO: Broadcasting message to conversation: 1 {"id":75,"content":"vdvsd","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:47:08","formatted_time":"14:47","conversation_id":1} 
[2025-08-05 14:47:09] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:47:09] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:47:09] local.INFO: 🔔 BACKEND: Message data:  {"id":75,"content":"vdvsd","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:47:08","formatted_time":"14:47","conversation_id":1} 
[2025-08-05 14:47:09] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:53:18] local.INFO: Broadcasting message to conversation: 1 {"id":76,"content":"oijiop","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:53:18","formatted_time":"14:53","conversation_id":1} 
[2025-08-05 14:53:19] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:53:19] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:53:19] local.INFO: 🔔 BACKEND: Message data:  {"id":76,"content":"oijiop","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:53:18","formatted_time":"14:53","conversation_id":1} 
[2025-08-05 14:53:20] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:53:39] local.INFO: Broadcasting message to conversation: 1 {"id":77,"content":"😗😘","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:53:38","formatted_time":"14:53","conversation_id":1} 
[2025-08-05 14:53:39] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:53:39] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:53:39] local.INFO: 🔔 BACKEND: Message data:  {"id":77,"content":"😗😘","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:53:38","formatted_time":"14:53","conversation_id":1} 
[2025-08-05 14:53:39] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:58:31] local.INFO: Broadcasting message to conversation: 1 {"id":78,"content":"vfvfd","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:58:31","formatted_time":"14:58","conversation_id":1} 
[2025-08-05 14:58:32] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:58:32] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:58:32] local.INFO: 🔔 BACKEND: Message data:  {"id":78,"content":"vfvfd","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:58:31","formatted_time":"14:58","conversation_id":1} 
[2025-08-05 14:58:32] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:58:52] local.INFO: Broadcasting message to conversation: 1 {"id":79,"content":"vsdsvds","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:58:52","formatted_time":"14:58","conversation_id":1} 
[2025-08-05 14:58:52] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 14:58:52] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 14:58:52] local.INFO: 🔔 BACKEND: Message data:  {"id":79,"content":"vsdsvds","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:58:52","formatted_time":"14:58","conversation_id":1} 
[2025-08-05 14:58:52] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 14:59:19] local.INFO: Broadcasting message to conversation: 1 {"id":80,"content":"vdsvsd","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:59:19","formatted_time":"14:59","conversation_id":1} 
[2025-08-05 14:59:19] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.51  
[2025-08-05 14:59:19] local.INFO: 🔔 BACKEND: Sender ID: 3, Receiver ID: 51  
[2025-08-05 14:59:19] local.INFO: 🔔 BACKEND: Message data:  {"id":80,"content":"vdsvsd","message_type":"text","attachments":[],"sender":{"id":3,"name":"Customer","profile_pic":"profile-images/eCxfhRHBSEdO7RHGYUJbC0oDWW67AdLMYPgdjrHg.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 14:59:19","formatted_time":"14:59","conversation_id":1} 
[2025-08-05 14:59:20] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
[2025-08-05 15:38:25] local.INFO: Broadcasting message to conversation: 1 {"id":81,"content":"vsvfsvs","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 15:38:25","formatted_time":"15:38","conversation_id":1} 
[2025-08-05 15:38:30] local.INFO: 🔔 BACKEND: Broadcasting message to user channel: user.3  
[2025-08-05 15:38:30] local.INFO: 🔔 BACKEND: Sender ID: 51, Receiver ID: 3  
[2025-08-05 15:38:30] local.INFO: 🔔 BACKEND: Message data:  {"id":81,"content":"vsvfsvs","message_type":"text","attachments":[],"sender":{"id":51,"name":"Damian Leach","profile_pic":"user-image/PByP1QQTS5xoeQl8yu9Sow93iOJQO7PweoFcnABi.png"},"is_sender":true,"sending_status":"sent","created_at":"2025-08-05 15:38:25","formatted_time":"15:38","conversation_id":1} 
[2025-08-05 15:38:30] local.INFO: 🔔 BACKEND: UserMessageReceived event broadcast successfully  
