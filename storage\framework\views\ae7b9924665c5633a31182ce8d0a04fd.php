<?php $__env->startPush('css'); ?>
    <style>
        .professional-swipper .swiper-slide {
            width: auto !important;
            flex-shrink: 0;
        }

        .professional-swipper .nav-item {
            margin: 0;
        }

        .professional-swipper .professional-tab {
            white-space: nowrap;
            min-width: fit-content;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section class="service padding">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-md-12 d-flex justify-content-between">
                    <h1 class="sora black  fs-34 semi_bold">Professional</h1>

                    <div class="search-bar d-flex align-items-center">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <input type="text" placeholder="Search">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="position-relative">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">

                            <!-- Swiper -->
                            <div class="swiper mySwiper professional-swipper">
                                <div class="swiper-wrapper">
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $loop_index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="swiper-slide">
                                            <li class="nav-item" role="presentation">
                                                <button
                                                    class="nav-link <?php echo e($category->slug == $active_category ? 'active' : ''); ?> professional-tab category-tab-btn"
                                                    id="pills-category-<?php echo e($category->slug); ?>-tab"
                                                    data-category-name="<?php echo e($category->slug); ?>" data-bs-toggle="pill"
                                                    data-bs-target="#pills-profile" type="button" role="tab"
                                                    aria-controls="pills-profile"
                                                    aria-selected="<?php echo e($category->slug == $active_category ? 'true' : 'false'); ?>">
                                                    <?php echo e($category->name); ?>

                                                </button>
                                            </li>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </ul>
                    </div>
                    <div id="subcategory-professional-container"></div>
                </div>
            </div>
        </div>

    </section>
    <?php echo $__env->make('website.template.modal.filter-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script>
        function RatedSwipper() {
            if ($(".top-rated-swiper").length) {
                var topRatedSwiper = new Swiper(".top-rated-swiper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 20,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true, 
                    },
                });
            }
        }
        const getProfessional = (category, subcategory) => {
            $.ajax({
                url: "<?php echo e(route('filter_professional')); ?>",
                type: "GET",
                data: {
                    category: category,
                    subcategory: subcategory
                },
                success: function(response) {
                    if (response.status === true) {
                        $('#subcategory-professional-container').html(response.data.page);

                        // Update the URL without reloading
                        let newUrl = `/professional/${category}`;
                        if (response.data.subcategory) {
                            newUrl += `/${response.data.subcategory}`;
                        }
                        window.history.pushState({
                            path: newUrl
                        }, '', newUrl);
                        RatedSwipper();
                    } else {
                        $('#subcategory-professional-container').html(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX Error:", status, error);
                    $('#subcategory-professional-container').html("An error occurred. Please try again.");
                }
            });
        }

        $(document).ready(function() {
            RatedSwipper();
            // Initialize Professional Swiper
            var professionalSwiper = new Swiper(".professional-swipper", {
                slidesPerView: '4',
                spaceBetween: 10,
                freeMode: true,
                navigation: {
                    nextEl: ".professional-next",
                    prevEl: ".professional-prev",
                },
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 5
                    },
                    640: {
                        slidesPerView: 3,
                        spaceBetween: 10
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 10
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 10
                    }
                }
            });

            // Initialize Top Rated Swiper if exists

            // Fetch default professionals on load
            getProfessional('<?php echo e($active_category); ?>', '<?php echo e($active_subcategory); ?>');

            // Handle tab button clicks
            $(document).on('click', '.category-tab-btn, .subcategory-tab-btn', function() {
                let category = $(this).data("category-name");
                let subcategory = $(this).data("subcategory-name");
                getProfessional(category, subcategory);
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/website/professional.blade.php ENDPATH**/ ?>