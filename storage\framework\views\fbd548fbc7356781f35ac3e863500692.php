<!-- Promo Bar -->
<div class="fixed-web-header">

    <div class=" text-white py-1 discount-header">
        <div class="container d-flex justify-content-between align-items-center py-5">
            <ul class="d-flex gap-4 mb-0 px-0 header-icon">
                <li><a href="<?php echo e(setting()->facebook ?? ''); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/facebook.svg"
                    onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'"
                            alt="Logo" class="img-fluid"></a></li>
                <li><a href="<?php echo e(setting()->whatsapp ?? ''); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/whatsapp.svg"
                     onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'"
                            alt="Logo" class="img-fluid"></a></li>
                <li><a href="<?php echo e(setting()->youtube ?? ''); ?>"><img src="<?php echo e(asset('website')); ?>/assets/images/youtube.svg"
                     onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'"
                            alt="Logo" class="img-fluid"></a></li>
            </ul>
            <div class="d-flex gap-4">
                <a href="mailto:<?php echo e(setting()->email ?? ''); ?>" class="fs-13 normal white"><i
                        class="fa-solid fa-envelope me-2" style="color: #ffffff;"></i> <?php echo e(setting()->email ?? ''); ?></a>
                <a href="tel:<?php echo e(setting()->phone ?? ''); ?>" class="fs-13 normal white"> <i class="fa-solid fa-phone me-2"
                        style="color: #ffffff;"></i> <?php echo e(setting()->phone ?? ''); ?></a>
            </div>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg bg-white header">
        <div class="container py-2">
            <!-- Logo -->
            <div class="d-flex gap-2 w-400px align-items-center">
                <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                    <img src="<?php echo e(asset('website') . '/' . setting()->logo); ?>" alt="Logo" class="img-fluid"
                        style="height: 37px;">
                </a>
                <?php if(auth()->check() && !auth()->user()->hasRole('admin')): ?>
                    <img src="<?php echo e(asset('website') . '/' . setting()->logo); ?>" alt="Logo" class="img-fluid"
                        style="height: 20px;">

                    <div class="custom-select-location" style="width:200px;">
                        <select>
                            <option value="0">Brazil, Rio De Janeiro</option>
                            <option value="1">Brazil, Rio De Janeiro</option>
                            <option value="2">Brazil, Rio De Janeiro</option>
                        </select>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Toggler for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>


            <!-- Navbar content -->
            <div class="collapse navbar-collapse flex-end align-items-center navbar-header gap-4" id="mainNavbar">
                <?php if(auth()->check() && auth()->user()->hasRole('individual')): ?>
                    <div class="app-navbar-item ms-1 user-info">
                        <i class="fas fa-star "> <span class="user-name sora fs-12"> INDIVIDUAL </span> </i>
                    </div>
                <?php elseif(auth()->check() && auth()->user()->hasRole('business')): ?>
                    <div class="app-navbar-item ms-1 user-info">
                        <i class="fas fa-star "> <span class="user-name sora fs-12"> BUSINESS </span> </i>
                    </div>
                <?php endif; ?>

                <div class="app-navbar-item ms-1 ">
                    <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px"
                        data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                        data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                        <i class="far fa-bell"></i> 
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                        </i>
                    </div>
                    <div class="menu notification-dropdown menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px p-5"
                        data-kt-menu="true" id="kt_menu_notifications">

                        <p class="fs-16 bold">Notifications</p>

                        <?php for($i = 0; $i < 4; $i++): ?>
                            <div
                                class="d-flex align-items-center gap-3 justify-content-center  border-bottom mb-5 pb-3">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/notification-user.png" alt="Logo"
                                    class="img-fluid" style="height: 50px;"> 
                                <div>
                                    <p class="fs-13 mb-0">Jenny Wilson Lorem Ipsum is simply dummy text of the printing.
                                    </p>
                                    <p class="fs-12 mb-0">5 min</p>
                                </div>
                            </div>
                        <?php endfor; ?>

                        <a href="<?php echo e(route('notification')); ?>" class="see-all-btn"> See All</a>
                    </div>
                </div>

                <div class="app-navbar-item ms-1">
                    <a href="<?php echo e(route('chats.index')); ?>" class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px position-relative">
                        <i class="far fa-envelope"></i>
                        <span id="envelope-counter" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none; font-size: 10px; min-width: 18px; height: 18px; line-height: 18px;">
                            0
                        </span>
                    </a>
                </div>

                <div lass="app-navbar-item ms-1">
                    <i class="far fa-question-circle mt-1"></i>
                </div>

                <div class="app-navbar-item ms-1 " id="kt_header_user_menu_toggle">
                    <div class="cursor-pointer symbol symbol-35px "
                        data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                        data-kt-menu-placement="bottom-end">
                        <?php if(!(auth()->user()->profile) || Auth::user()->profile->pic == null): ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/blank.png" class="rounded-pill"
                                alt="user" />
                        <?php else: ?>
                            <img alt="Logo"
                                src="<?php echo e(asset('website') . '/' . auth()->user()->profile->pic); ?>"
                                onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'" />
                        <?php endif; ?>
                    </div>
                    <div class="menu  menu-sub menu-sub-dropdown right-sidebar-menus menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px"
                        data-kt-menu="true">
                        <div class="menu-item px-3">
                            <div class="menu-content d-flex align-items-center px-3">
                                <div class="symbol symbol-50px me-5">
                                    <?php if(!(auth()->user()->profile) || auth()->user()->profile->pic == null): ?>
                                        <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/blank.png"
                                            class="rounded-pill" alt="user" />
                                    <?php else: ?>
                                        <img alt="Logo"
                                            src="<?php echo e(asset('website').'/'.auth()->user()->profile->pic); ?>" />
                                    <?php endif; ?>
                                </div>
                                <div class="d-flex flex-column">
                                    <div class="fw-bold d-flex align-items-center fs-5"><?php echo e(Auth::user()->name ?? ''); ?>

                                    </div>
                                    <a href="#"
                                        class="fw-semibold deep-blue fs-7"><?php echo e(Auth::user()->email ?? ''); ?></a>
                                </div>
                            </div>
                        </div>
                        <div class="separator my-2"></div>
                        <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(route('profile_setting')); ?>" class="menu-link px-5">Profile</a>
                        </div>
                        <?php else: ?>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(route('profile_settings')); ?>" class="menu-link px-5">Profile</a>
                        </div>
                        <?php endif; ?>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(route('setting')); ?>" class="menu-link px-5">Settings</a>
                        </div>
                        <div class="separator my-2"></div>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(url('logout')); ?>" class="menu-link px-5 logout">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="bg-white border-top header-items">
        <div class="container">
            <ul class="nav justify-content-start py-4 gap-7">
                <?php if(auth()->check() &&
                        auth()->user()->hasAnyRole(['professional', 'individual', 'business'])): ?>
                    <!-- Dashboard Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('dashboard')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('dashboard')); ?>">
                            Dashboard
                        </a>
                    </li>

                    <!-- For Business Role, show Staff first, then Booking -->
                    <?php if(auth()->user()->hasRole('business')): ?>
                        <!-- Staff Menu Item -->
                        <li class="nav-item">
                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('staffs') || request()->is('staffs/create') || request()->is('staffs/{staff}')): ?> active <?php endif; ?>"
                                href="<?php echo e(route('staffs.index')); ?>">
                                Staff
                            </a>
                        </li>
                    <?php endif; ?>
                    <!-- Booking Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('booking')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('booking')); ?>">
                            Booking
                        </a>
                    </li>

                    <!-- Chats Menu Item -->
                    

                    <!-- Common Menu Items for Both Individual and Business -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->routeIs('services.*')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('services.index')); ?>">
                            Services
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('earning')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('earning')); ?>">
                            Earnings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('subscriptions')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('subscriptions.index')); ?>">
                            Subscription
                        </a>
                    </li>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('discountcoupons-list')): ?>
                        <li class="nav-item">
                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('discount-coupons') || request()->is('discount-coupons/create')): ?> active <?php endif; ?>"
                                href="<?php echo e(route('discount-coupons.index')); ?>">
                                Discount & Coupon
                            </a>
                        </li>
                    <?php endif; ?>

                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('business_analytics')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('business_analytics')); ?>">
                            Analytics
                        </a>
                    </li>
                <?php endif; ?>
                <!-- Admin Role Menu -->
                <!-- <?php if(auth()->check() &&
                        auth()->user()->hasRole(['admin'])): ?>
                    <div class="position-relative">
                        <div class="swiper mySwiper header-swiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('dashboard')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('dashboard')); ?>">
                                            Dashboard
                                        </a>
                                    </li>
                                </div>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('categories-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('categories')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('categories.index')); ?>">
                                                Categories
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('professionals')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('professionals')); ?>">
                                            Professionals
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('customers')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('customers')); ?>">
                                            Customers
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('booking')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('booking')); ?>">
                                            Bookings
                                        </a>
                                    </li>
                                </div>


                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('refund_request')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('refund_request')); ?>">
                                            Refund Requests
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('wallet')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('wallet')); ?>">
                                            Wallet
                                        </a>
                                    </li>
                                </div>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('vatmanagements-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('vatmanagements')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('vatmanagements.index')); ?>">
                                                VAT Management
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('discountcoupons-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('discount-coupons', 'discount-coupons/create')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('discount-coupons.index')); ?>">
                                                Discount & Coupon
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscriptions-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('subscriptions')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('subscriptions.index')); ?>">
                                                Subscription Management
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('holidays-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('holidays')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('holidays.index')); ?>">
                                                Holidays
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('certifications-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('certifications')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('certifications.index')); ?>">
                                                Certifications
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cms-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('cms/home')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('cms.home')); ?>">
                                                CMS
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('countries-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('countries')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('countries.index')); ?>">
                                                Countries
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-list')): ?>
                                    <div class="swiper-slide">
                                        <li class="nav-item">
                                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('settings')): ?> active <?php endif; ?>"
                                                href="<?php echo e(route('settings.index')); ?>">
                                                Settings
                                            </a>
                                        </li>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                <?php endif; ?> -->

                <?php if(auth()->guard()->guest()): ?>
                    <li class="nav-item ms-auto"><a class="nav-link  deep-blue fs-14 sora semi_bold"
                            href="<?php echo e(url('register')); ?>">Become a professional →</a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/layout/header.blade.php ENDPATH**/ ?>