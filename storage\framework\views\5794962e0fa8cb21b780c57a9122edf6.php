<?php $__env->startSection('head'); ?>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <style>
        /* Registration Loader Styles */
        #registrationLoader .logo-loader {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        #registrationLoader .logo-container {
            text-align: center;
        }

        #registrationLoader .circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        /* Hide steps initially to prevent flash */
        .step {
            display: none;
        }

        .step:first-child {
            display: block;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <!-- Registration Loader -->
    <div id="registrationLoader"
        style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.9); z-index: 9999; display: flex; justify-content: center; align-items: center;">
        <div class="logo-loader">
            <div class="logo-container">
                <div class="circle">
                    <img src="<?php echo e(asset('website') . '/' . setting()->loader_logo); ?>" class="h-100 w-100 object-fit-contain"
                        alt="logo">
                </div>
            </div>
        </div>
    </div>
    <div class="container professional-acc-form">
        <div class="row justify-content-center">
            <div class="col-md-12 stepper-navigation">
                <ul id="progressbar">
                    <li id="account" class="active"></li>
                    <li id="personal"></li>
                    <li id="payment"></li>
                    <li id="confirm"></li>
                    <li id="confirm"></li>
                </ul>

                <div class="pro-stepper-header d-flex justify-content-between align-items-center">
                    <div>
                        <!-- Home Logo -->
                        <a href="<?php echo e(url('/')); ?>" class="home-logo">
                            <img src="<?php echo e(asset('website') . '/' . setting()->logo); ?>" alt="Home" class="img-fluid"
                                style="height: 60px;">
                        </a>

                        <!-- Previous Button -->
                        <i name="previous" value="ll"
                            class="fas fa-chevron-left previous action-button-previous opacity-0"></i>
                    </div>

                    <!-- Continue Button Container -->
                    <div class="continue-btn-container">
                        <div class="d-flex flex-column">
                            <a class=" blue-border-btn mb-3" href="#" id="logoutBtnSubmit">Logout</a>
                            <input type="button" name="next" class=" next action-button" value="Continue" />
                        </div>
                    </div>

                    <!-- Submit Button Container (for final step) -->
                    <div class="submit-btn-container" style="display: none;">
                        <div class="d-flex flex-column">
                            <a class=" blue-border-btn mb-3" href="#" id="logoutBtnSubmit">Logout</a>
                            <input type="button" class="submit action-button" value="Continue" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8 mb-2 pt-20 mt-10">
                <div class=" px-0 pt-4 pb-0 mt-10 mb-3">
                    <form id="acc-form" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <fieldset data-step="1" class="step" id="step-1">
                            <?php echo $__env->make('dashboard.templates.professional-acc-stepper.step1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </fieldset>

                        <fieldset data-step="2" class="step" id="step-2">
                            <?php echo $__env->make('dashboard.templates.professional-acc-stepper.step2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </fieldset>

                        <fieldset data-step="3" class="step" id="step-3">
                            <?php echo $__env->make('dashboard.templates.professional-acc-stepper.step3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </fieldset>

                        <fieldset data-step="4" class="step" id="step-4">
                            <?php echo $__env->make('dashboard.templates.professional-acc-stepper.step4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </fieldset>

                        <fieldset data-step="5" class="step" id="step-5">
                            <?php echo $__env->make('dashboard.templates.professional-acc-stepper.step5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </fieldset>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.validate/1.7/additional-methods.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>

    <script>
        let holidayIndex = <?php echo e($holidays->count()); ?>;
        let certIndex = <?php echo e(auth()->user()->certificates->count() > 0 ? auth()->user()->certificates->count() - 1 : 0); ?>;
    </script>

    <script>
        document.querySelectorAll('.sub_category[id^="sub_category_"][data-sub-ctg]').forEach((subCat) => {
            const subCtgValue = subCat.getAttribute('data-sub-ctg');
            const checkboxes = subCat.querySelectorAll('input[type="checkbox"]');
            const matchingService = document.querySelector(`.service_category[data-ctg="${subCtgValue}"]`);

            if (!matchingService) return;

            const radioBox = matchingService.querySelector('.radio-box');
            if (!radioBox) return;

            const getCheckedCount = () => [...checkboxes].filter(cb => cb.checked).length;

            const updateBorder = () => {
                const checkedCount = getCheckedCount();
                radioBox.style.border = checkedCount > 0 ? '2px solid #020C87' : '';
            };

            // Initial check
            updateBorder();

            checkboxes.forEach((checkbox) => {
                checkbox.addEventListener('change', function (e) {
                    const checkedCount = getCheckedCount();

                    if (this.checked && checkedCount > 3) {
                        this.checked = false;

                        Swal.fire({
                            icon: 'warning',
                            title: 'Maximum Limit Reached',
                            html: '<p style="font-size: 16px; color: #666;">You can select a maximum of 3 options</p>',
                            confirmButtonText: 'Got it',
                            confirmButtonColor: '#020C87',
                            buttonsStyling: true,
                            backdrop: `
                                rgba(0,0,0,0.4)
                                url("/images/nyan-cat.gif")
                                left top
                                no-repeat
                            `,
                            showClass: {
                                popup: 'animate__animated animate__zoomIn animate__faster'
                            },
                            hideClass: {
                                popup: 'animate__animated animate__zoomOut animate__faster'
                            },
                            customClass: {
                                container: 'my-swal-container',
                                popup: 'my-swal-popup',
                                title: 'my-swal-title',
                                confirmButton: 'my-swal-confirm-btn'
                            }
                        });
                        return;
                    }

                    if (!this.checked && checkedCount === 0) {
                        this.checked = true;

                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            html: '<p style="font-size: 16px; color: #666; margin-bottom: 0;">Please select at least 1 subcategory</strong>.</p>',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#020C87',
                            showClass: {
                                popup: 'animate__animated animate__shakeX'
                            },
                            hideClass: {
                                popup: 'animate__animated animate__fadeOutUp'
                            },
                            customClass: {
                                container: 'my-swal-container',
                                popup: 'my-swal-popup',
                                title: 'my-swal-title',
                                confirmButton: 'my-swal-confirm-btn'
                            }
                        });
                        return;
                    }

                    updateBorder();
                });
            });
        });
    </script>
    <script>
        const input = document.querySelector("#phone"); // example selector
        let professionalIti = null;
        if (input) {
            professionalIti = window.intlTelInput(input, {
                initialCountry: "auto",
                geoIpLookup: function (callback) {
                    fetch("https://ipinfo.io/json?token=1e240fc8539ff6")
                        .then((resp) => resp.json())
                        .then((resp) => {
                            const countryCode = resp && resp.country ? resp.country : "us";
                            callback(countryCode);
                        })
                        .catch(() => callback("us")); // fallback
                },
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
            });

            // Add phone validation for professional form
            input.addEventListener('blur', function() {
                const phoneError = document.getElementById('professional-phone-error');
                if (phoneError) {
                    phoneError.remove();
                }

                if (input.value.trim() && !professionalIti.isValidNumber()) {
                    // Create error message element
                    const errorElement = document.createElement('span');
                    errorElement.id = 'professional-phone-error';
                    errorElement.className = 'invalid-feedback';
                    errorElement.style.display = 'block';
                    errorElement.style.color = 'red';
                    errorElement.style.fontWeight = 'bold';
                    errorElement.innerHTML = '<strong>Please enter a valid phone number</strong>';

                    // Insert after phone input
                    input.parentNode.appendChild(errorElement);
                    input.classList.add('error-input');
                } else {
                    input.classList.remove('error-input');
                }
            });

            // Clear error on input change
            input.addEventListener('input', function() {
                const phoneError = document.getElementById('professional-phone-error');
                if (phoneError) {
                    phoneError.remove();
                }
                input.classList.remove('error-input');
            });
        }
    </script>

    <script>
        const fileInput = document.querySelector('input[type="file"]');
        const wrapper = document.querySelector('.image-input-wrapper');
        const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
        const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
        const imageInput = document.querySelector('.image-input');

        fileInput.addEventListener('change', function (e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();

                reader.onload = function (event) {
                    wrapper.style.backgroundImage = `url('${event.target.result}')`;
                    imageInput.classList.remove('image-input-empty');
                };

                reader.readAsDataURL(file);
            }
        });

        // Remove action
        removeBtn.addEventListener('click', function () {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
            // Set hidden input for backend if needed
            document.querySelector('input[name="avatar_remove"]').value = '1';
        });

        // Optional: Cancel action (if you need to reset back to default image, add that logic)
        cancelBtn.addEventListener('click', function () {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
        });
    </script>

    <script>
        $(document).ready(function () {
            var current_fs, next_fs, previous_fs;
            var opacity;
            var current = 1;
            var steps = $("fieldset").length;

            // Load saved progress on page load
            loadSavedProgress();

            setProgressBar(current);
            toggleSubmitButton(current); // Initial check

            $(".next").click(function () {
                current_fs = $("fieldset:visible");

                // Validate current step before proceeding
                if (validateFields(current_fs)) {
                    return false; // Stop if validation fails
                }

                // Save current step data before proceeding
                saveStepData(current_fs.data('step'), function (success, redirectUrl) {
                    if (success) {
                        proceedToNextStep();
                    } else {
                        // alert('Failed to save step data. Please try again.');
                    }
                });
            });

            function proceedToNextStep() {
                current_fs = $("fieldset:visible");
                next_fs = current_fs.next("fieldset");
                if (next_fs.length === 0) return;

                $("#progressbar li").eq($("fieldset").index(next_fs)).addClass("active");

                next_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        next_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });

                if ($('.action-button-previous').hasClass('opacity-0')) {
                    $('.action-button-previous').removeClass('opacity-0');
                }

                setProgressBar(++current);
                toggleSubmitButton(current);
            }

            function saveStepData(stepNumber, callback) {
                const formArray = $('#acc-form').serializeArray();
                const formData = new FormData();
                formArray.forEach(function (field) {
                    formData.append(field.name, field.value);
                });

                // Handle file inputs separately (serializeArray doesn't capture files)
                const fileInputs = $('#acc-form input[type="file"]');

                fileInputs.each(function (index, input) {
                    const files = input.files;

                    if (files && files.length > 0) {
                        for (let i = 0; i < files.length; i++) {
                            formData.append(input.name, files[i]);
                        }
                    }
                });

                // Add step number and CSRF token
                formData.append('step', stepNumber);
                formData.append('_token', '<?php echo e(csrf_token()); ?>');

                $.ajax({
                    url: '<?php echo e(route('register.professional.save_step')); ?>',
                    type: 'POST',
                    data: formData,
                    processData: false, // Important for file uploads
                    contentType: false, // Important for file uploads
                    success: function (response) {
                        if (response.success) {
                            callback(true, response.redirect);
                        } else {
                            callback(false);
                            Swal.fire({
                                title: "Error",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('AJAX Error:', error);
                        console.error('Response:', xhr.responseText);
                        callback(false);
                    }
                });
            }

            function loadSavedProgress() {
                $.ajax({
                    url: '<?php echo e(route('register.professional.get_progress')); ?>',
                    type: 'GET',
                    success: function (response) {
                        if (response.success && response.current_step) {
                            current = parseInt(response.current_step);

                            // Hide all steps
                            $("fieldset").hide();

                            // Show current step
                            $(`#step-${current}`).show();

                            // Update progress bar
                            for (let i = 1; i <= current; i++) {
                                $("#progressbar li").eq(i - 1).addClass("active");
                            }

                            setProgressBar(current);
                            toggleSubmitButton(current);

                            // Show previous button if not on first step
                            if (current > 1) {
                                $('.action-button-previous').removeClass('opacity-0');
                            }
                        }

                        // Hide loader after progress is loaded
                        $('#registrationLoader').fadeOut(300);
                    },
                    error: function () {
                        console.log('Could not load saved progress');
                        // Hide loader even on error
                        $('#registrationLoader').fadeOut(300);
                    }
                });
            }

            $(".previous").click(function () {
                current_fs = $("fieldset:visible");
                previous_fs = current_fs.prev("fieldset");

                if (previous_fs.length === 0) return;

                $("#progressbar li").eq($("fieldset").index(current_fs)).removeClass("active");

                previous_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        previous_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });

                stepNum = current_fs.attr('data-step');
                if (stepNum == '2') {
                    $('.action-button-previous').addClass('opacity-0');
                }

                setProgressBar(--current);
                toggleSubmitButton(current);
            });

            function setProgressBar(curStep) {
                var percent = parseFloat(100 / steps) * curStep;
                percent = percent.toFixed();
                $(".progress-bar").css("width", percent + "%");
            }

            function toggleSubmitButton(currentStep) {
                console.log('toggleSubmitButton called with step:', currentStep, 'total steps:', steps);
                if (currentStep === steps) {
                    console.log('Showing submit button, hiding continue button');
                    $('.submit-btn-container').show();
                    $('.continue-btn-container').hide();
                } else {
                    console.log('Showing continue button, hiding submit button');
                    $('.submit-btn-container').hide();
                    $('.continue-btn-container').show();
                }
            }

            $(".submit").click(function (e) {
                e.preventDefault();

                // Validate current step (step 5) before submission
                current_fs = $("fieldset:visible");
                if (validateFields(current_fs)) {
                    return false; // Stop if validation fails
                }

                // Save step 5 data and redirect to dashboard
                saveStepData(current_fs.data('step'), function (success, redirectUrl) {
                    if (success) {
                        // Step 5 data saved successfully, redirect to dashboard
                        if (redirectUrl) {
                            window.location.href = redirectUrl;
                        } else {
                            window.location.href = '<?php echo e(route('dashboard')); ?>';
                        }
                    } else {
                        // alert('Failed to save step data. Please try again.');
                    }
                });
            });

            // Helper function to convert 12-hour format to 24-hour format for comparison
            function convertTo24Hour(time12h) {
                const [time, modifier] = time12h.split(' ');
                let [hours, minutes] = time.split(':');
                if (hours === '12') {
                    hours = '00';
                }
                if (modifier === 'PM') {
                    hours = parseInt(hours, 10) + 12;
                }
                return `${hours}:${minutes}`;
            }

            function validateFields($fieldset) {
                var hasError = false;
                $fieldset.find(
                    'input:not(.no_validate,[type="hidden"], [type="checkbox"], .select2-search__field, .next, .previous), textarea:not(.no_validate)'
                ).each(function () {
                    if (!$(this).val()) {
                        $(this).addClass("valid_error");
                        $(this).css('border', '2px solid red');
                        hasError = true;
                    } else {
                        $(this).removeClass("valid_error");
                        $(this).css('border', '0.5px solid #9b9b9b');
                    }
                });

                // Special validation for step 2 (categories and subcategories)
                if ($fieldset.attr('id') === 'step-2') {
                    console.log('Validating step 2...');

                    // Check selected categories (active service_category elements)
                    var selectedCategories = $fieldset.find('.service_category.active').length;
                    console.log('Selected categories:', selectedCategories);

                    if (selectedCategories < 1) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least 1 category",
                            icon: "error"
                        });
                        hasError = true;
                    } else if (selectedCategories > 3) {
                        Swal.fire({
                            title: "Error",
                            text: "You can select maximum 3 categories",
                            icon: "error"
                        });
                        hasError = true;
                    }

                    // Check selected subcategories
                    var selectedSubcategories = $fieldset.find('input[name="subcategories[]"]:checked').length;
                    console.log('Selected subcategories:', selectedSubcategories);

                    if (selectedSubcategories < 1) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least 1 subcategory",
                            icon: "error"
                        });
                        hasError = true;
                    }
                }

                // Special validation for step 3 (certifications)
                if ($fieldset.attr('id') === 'step-3') {
                    console.log('Validating step 3...');

                    // Product certifications are always required
                    var selectedProductCerts = $fieldset.find('input[name="product_certifications[]"]:checked').length;
                    console.log('Selected product certifications:', selectedProductCerts);

                    if (selectedProductCerts < 1) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least one product certification",
                            icon: "error"
                        });
                        hasError = true;
                    }

                    // Check if any certificate fields are filled
                    var hasCertificateData = false;
                    var certificateBlocks = $fieldset.find('.file-upload-group');

                    certificateBlocks.each(function() {
                        var $block = $(this);
                        var title = $block.find(`input[name*="[title]"]`).val();
                        var issuedBy = $block.find(`input[name*="[issued_by]"]`).val();
                        var issuedDate = $block.find(`input[name*="[issued_date]"]`).val();
                        var endDate = $block.find(`input[name*="[end_date]"]`).val();

                        if (title || issuedBy || issuedDate || endDate) {
                            hasCertificateData = true;
                            return false; // Break the loop
                        }
                    });

                    // Validate certificates only if there's certificate data
                    if (hasCertificateData) {
                        var certificateErrors = {};

                        certificateBlocks.each(function(index) {
                            var $block = $(this);
                            var blockIndex = index;
                            var certNumber = blockIndex + 1;

                            // Check if any field is filled for this certificate
                            var title = $block.find(`input[name*="[title]"]`).val();
                            var issuedBy = $block.find(`input[name*="[issued_by]"]`).val();
                            var issuedDate = $block.find(`input[name*="[issued_date]"]`).val();
                            var endDate = $block.find(`input[name*="[end_date]"]`).val();

                            var hasAnyField = title || issuedBy || issuedDate || endDate;

                            if (hasAnyField) {
                                var certErrors = [];

                                // If any field is filled, all fields become required
                                if (!title) {
                                    certErrors.push(`Certificate title is required`);
                                }
                                if (!issuedBy) {
                                    certErrors.push(`Issued by is required`);
                                }
                                if (!issuedDate) {
                                    certErrors.push(`Issued date is required`);
                                }
                                if (!endDate) {
                                    certErrors.push(`Expiry date is required`);
                                }

                                // Check if expiry date is after issued date
                                if (issuedDate && endDate && new Date(endDate) <= new Date(issuedDate)) {
                                    certErrors.push(`Expiry date must be after issued date`);
                                }

                                // Check image or exception logic
                                var hasException = $block.find(`input[name*="[exception]"]`).is(':checked');
                                var hasImage = $block.find(`input[name*="[image]"]`)[0]?.files?.length > 0 ||
                                             $block.find(`input[name*="[old_image]"]`).length > 0;
                                var exceptionReason = $block.find(`textarea[name*="[exception_reason]"]`).val();

                                if (hasException) {
                                    // If exception is checked, reason is required
                                    if (!exceptionReason) {
                                        certErrors.push(`Reason for exception is required when certificate exception is checked`);
                                    }
                                } else {
                                    // If no exception, image is required
                                    if (!hasImage) {
                                        certErrors.push(`Certificate image is required or check certificate exception`);
                                    }
                                }

                                // If this certificate has errors, add them to the main errors object
                                if (certErrors.length > 0) {
                                    certificateErrors[certNumber] = certErrors;
                                }
                            }
                        });

                        // Show all errors grouped by certificate if any exist
                        if (Object.keys(certificateErrors).length > 0) {
                            var errorMessage = '';

                            Object.keys(certificateErrors).forEach(function(certNumber) {
                                errorMessage += `<strong>Certificate #${certNumber}:</strong><br>`;
                                certificateErrors[certNumber].forEach(function(error) {
                                    errorMessage += `• ${error}<br>`;
                                });
                                errorMessage += '<br>';
                            });

                            Swal.fire({
                                title: "Validation Errors",
                                html: errorMessage,
                                icon: "error"
                            });
                            hasError = true;
                        }
                    }
                }

                // Special validation for step 4 (availability)
                if ($fieldset.attr('id') === 'step-4') {
                    var anyDayChecked = $fieldset.find('input[name^="availability"][type="checkbox"]:checked')
                        .length > 0;
                    if (!anyDayChecked) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least one day for availability",
                            icon: "error"
                        });
                        hasError = true;
                    } else {
                        // Check if time is provided for each selected day and validate time ranges
                        var timeErrors = [];

                        $fieldset.find('input[name^="availability"][type="checkbox"]:checked').each(function() {
                            var checkboxName = $(this).attr('name');
                            // Extract the index from the checkbox name (e.g., availability[0][day] -> 0)
                            var indexMatch = checkboxName.match(/availability\[(\d+)\]\[day\]/);
                            if (indexMatch) {
                                var index = indexMatch[1];
                                var dayName = $(this).val();
                                var startTime = $fieldset.find(
                                    `input[name="availability[${index}][start]"]`).val();
                                var endTime = $fieldset.find(`input[name="availability[${index}][end]"]`)
                                    .val();

                                if (!startTime || !endTime) {
                                    timeErrors.push(`Please provide both start and end time for ${dayName}`);
                                } else {
                                    // Convert 12-hour format to 24-hour for comparison
                                    var startTime24 = convertTo24Hour(startTime);
                                    var endTime24 = convertTo24Hour(endTime);

                                    if (startTime24 >= endTime24) {
                                        timeErrors.push(`End time must be after start time for ${dayName}`);
                                    }
                                }
                            }
                        });

                        // Validate holiday times
                        $fieldset.find('.time-picker-calendar input[type="checkbox"]:checked').each(function() {
                            var $holidayContainer = $(this).closest('.time-picker-calendar');
                            var $timeContainer = $holidayContainer.find('.start-time');
                            var holidayName = $(this).siblings('.checkmark').text().trim();

                            if ($timeContainer.is(':visible')) {
                                var startTimeInput = $timeContainer.find('input[name*="[start_time]"]');
                                var endTimeInput = $timeContainer.find('input[name*="[end_time]"]');
                                var startTime = startTimeInput.val();
                                var endTime = endTimeInput.val();

                                // If holiday is checked and time container is visible, both times are required
                                if (!startTime || !endTime) {
                                    timeErrors.push(`Please provide both start and end time for ${holidayName}`);
                                } else {
                                    // If both times are provided, validate the range
                                    var startTime24 = convertTo24Hour(startTime);
                                    var endTime24 = convertTo24Hour(endTime);

                                    if (startTime24 >= endTime24) {
                                        timeErrors.push(`End time must be after start time for ${holidayName}`);
                                    }
                                }
                            }
                        });

                        if (timeErrors.length > 0) {
                            var errorMessage = timeErrors.join('<br>');
                            Swal.fire({
                                title: "Time Validation Errors",
                                html: errorMessage,
                                icon: "error"
                            });
                            hasError = true;
                        }
                    }
                }

                // Special validation for step 5 (banner image)
                if ($fieldset.attr('id') === 'step-5') {
                    var bannerImageValue = $('#bannerImage').val();
                    var hasPreview = $('#profile_dzPreviews').children().length > 0;

                    if (!bannerImageValue || !hasPreview) {
                        Swal.fire({
                            title: "Error",
                            text: "Please upload a banner image",
                            icon: "error"
                        });
                        hasError = true;
                    }
                }
                return hasError; // Returns true if there is an error, false otherwise
            }

            // Handle logout with confirmation for both buttons
            $(document).on('click', '#logoutBtn, #logoutBtnSubmit', function (e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Registration Not Complete',
                    text: 'Your registration is not complete yet. You can continue from here anytime. Are you sure you want to logout?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, Logout',
                    cancelButtonText: 'Continue Registration'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = "<?php echo e(url('logout')); ?>";
                    }
                });
            });
        });
    </script>

    <script>
        // form submit
        $(document).on("click", ".continue-btn", function () {
            let form_data = $("#acc-form").serialize();
            console.log(form_data);

        })
        // form submit end

        function initFileUpload($group) {
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];
            const $dropArea = $group.find(".upload-box");
            const $fileInput = $group.find("input[type='file']");
            const $previewArea = $group.find(".preview-container");

            $dropArea.off();
            $fileInput.off();

            $dropArea.on("click", function () {
                $fileInput.click();
            });

            $fileInput.on("change", function (e) {
                const file = e.target.files[0];
                if (file) handleFile(file);
            });

            $dropArea.on("dragover", function (e) {
                e.preventDefault();
                $dropArea.css("background", "#eee");
            });

            $dropArea.on("dragleave", function (e) {
                e.preventDefault();
                $dropArea.css("background", "");
            });

            $dropArea.on("drop", function (e) {
                e.preventDefault();
                $dropArea.css("background", "");
                const file = e.originalEvent.dataTransfer.files[0];
                if (file) handleFile(file);
            });

            function handleFile(file) {
                if (!allowedImages.includes(file.type)) {
                    alert("Only JPG and PNG images are allowed.");
                    return;
                }

                if (file.size > 2 * 1024 * 1024) {
                    alert("File is too large. Max size: 2MB.");
                    return;
                }

                $previewArea.empty();
                // Don't clear the file input value - we need it for form submission
                // $fileInput.val("");

                const reader = new FileReader();
                reader.onload = function (e) {
                    const $preview = $(`
                                            <div class="preview-box" style="position: relative; display: inline-block; margin: 10px;">
                                                <img src="${e.target.result}" >
                                                    <button class="remove-preview" style="position: absolute; top: 2px; right: 2px; background: red; color: white; border: none; border-radius: 50%; width: 20px; height: 20px;">×</button>
                                                </div>
                                            `);

                    $preview.find(".remove-preview").on("click", function () {
                        $preview.remove();
                        $fileInput.val("");
                    });

                    $previewArea.append($preview);
                };
                reader.readAsDataURL(file);
            }
        }

        function reindexRows() {
            // Count existing certificates from the initial load (those with auth data)
            const existingCertsCount = <?php echo e(auth()->user()->certificates->count()); ?>;

            $('#certifications-wrapper .file-upload-group').each(function (index) {
                const $group = $(this);

                $group.find('[name]').each(function () {
                    let name = $(this).attr('name');
                    if (name) {
                        name = name.replace(/certificates\[\d+\]/, `certificates[${index}]`);
                        $(this).attr('name', name);
                    }
                });

                $(this).find('textarea[id^="w3review_"]').attr('id', `w3review_${index}`);
                $(this).find('label[for^="w3review_"]').attr('for', `w3review_${index}`);
                $(this).find('h1').text(`Certificate #${index + 1}`);

                // Ensure delete button exists for each certificate block
                let deleteBtn = $(this).find('.delete-block');
                if (deleteBtn.length === 0) {
                    $(this).append(`
                                                <div class="mt-3 d-flex justify-content-between">
                                                    <button type="button" class="delete-block">Delete This Block</button>
                                                </div>
                                            `);
                }
            });
            certIndex = $('#certifications-wrapper .file-upload-group').length - 1;
        }

        function addCertificationBlock() {
            certIndex++;
            const wrapper = document.getElementById('certifications-wrapper');
            const totalCerts = $('#certifications-wrapper .file-upload-group').length + 1;

            const newBlock = `
                            <div class="gray-card my-5 file-upload-group">
                                <h1>Certificate #${certIndex + 1}</h1>
                                <div class="col-md-12">
                                    <label class="fieldlabels">Certification Title*</label>
                                    <input type="text" name="certificates[${certIndex}][title]" placeholder="Enter certification title"/>
                                    <label class="fieldlabels">Issued by*</label>
                                    <input class="no_validate" type="text" name="certificates[${certIndex}][issued_by]" placeholder="Enter name"/>
                                </div>

                                <div class="col-md-6">
                                    <label class="fieldlabels">Issued Date*</label>
                                    <input class="no_validate" type="date" name="certificates[${certIndex}][issued_date]" placeholder="Enter issued date"/>
                                </div>

                                <div class="col-md-6">
                                    <label class="fieldlabels">End Date*</label>
                                    <input class="no_validate" type="date" name="certificates[${certIndex}][end_date]" placeholder="Enter end date"/>
                                </div>

                                                <div class="col-md-12 form-border">
                                                    <p class="manrope fw-600 light-black">Share Certificates</p>
                                                    <div>
                                                        <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans" style="cursor:pointer;">
                                                            <img src="<?php echo e(asset('website/assets/images/upload.svg')); ?>" alt="Upload Icon">
                                                            <p>Upload Certificate</p>
                                                            <p class="mb-0">Maximum file size: 2 MB</p>
                                                            <p>Supported format: JPG and PNG</p>
                                                            <span class="add-file">
                                                                <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                                            </span>
                                                            <input class="no_validate" name="certificates[${certIndex}][image]" type="file" hidden>
                                                        </label>
                                                    </div>
                                                    <div class="preview-container"></div>

                                                    <div class="exception-checkbox">
                                                        <label class="cert-excep">
                                                            <input class="no_validate" type="checkbox" id="exceptionToggle" name="certificates[${certIndex}][exception]">
                                                            <span class="checkmark">Certificate Exception</span>
                                                        </label>

                                        <div class="exception-textarea">
                                            <label class="mb-2" for="w3review_${certIndex}">Reason for Exception</label>
                                            <textarea class="mb-0 no_validate" id="w3review_${certIndex}" name="certificates[${certIndex}][exception_reason]" rows="4" cols="50"
                                            placeholder="Write reason for exception"></textarea>
                                        </div>
                                    </div>
                                </div>

                                                <div class="mt-3 d-flex justify-content-between">
                                                    <button type="button" class=" delete-block">Delete This Block</button>

                                                </div>
                                            </div>
                                        `;

            // Insert after the last block
            wrapper.insertAdjacentHTML('beforeend', newBlock);

            // Re-initialize upload and bind delete/add buttons for the new block
            const $newGroup = $('.file-upload-group').last();
            initFileUpload($newGroup);

            // Delete handler
            $newGroup.find('.delete-block').on('click', function () {
                $newGroup.remove();
                reindexRows();
                updateDeleteButtonVisibility();
            });

            // Add More handler inside block
            $newGroup.find('.add-more-inside').on('click', function () {
                addCertificationBlock();
            });

            // Update delete button visibility after adding new block
            updateDeleteButtonVisibility();
        }

        // On top Add More button click
        document.getElementById('addMoreBtn').addEventListener('click', addCertificationBlock);

        $(document).ready(function () {
            $('.file-upload-group').each(function () {
                initFileUpload($(this));
            });

            // Initialize delete buttons for existing certificates
            $(document).on('click', '.delete-block', function () {
                const $group = $(this).closest('.file-upload-group');
                const $grayCard = $(this).closest('.gray-card');

                // Remove the certificate block
                if ($group.length > 0) {
                    $group.remove();
                } else if ($grayCard.length > 0) {
                    $grayCard.remove();
                }

                reindexRows();
                updateDeleteButtonVisibility();
            });

            function updateDeleteButtonVisibility() {
                const totalCerts = $('#certifications-wrapper .file-upload-group').length;
                const initialCertBlock = $('.gray-card').not('#certifications-wrapper .gray-card').length;
                const totalAllCerts = totalCerts + initialCertBlock;

                // Update delete button visibility for all certificate blocks
                $('.delete-block').each(function () {
                    if (totalAllCerts > 1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            // Initial reindexing to set up delete buttons properly
            reindexRows();
            updateDeleteButtonVisibility();

            $(document).on("dragover drop", function (e) {
                e.preventDefault();
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Initialize all flatpickr time inputs
            flatpickr(".flatpickr-time", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "h:i K",
                time_24hr: false
            });

            // Ensure already visible time inputs (for checked holidays) are properly initialized
            document.querySelectorAll('.time-picker-calendar').forEach(function (calendar) {
                const checkbox = calendar.querySelector('.day-checkbox');
                const timeContainer = calendar.querySelector('.start-time');

                if (checkbox && checkbox.checked && timeContainer) {
                    // Make sure the time container is visible
                    timeContainer.style.display = 'flex';

                    // Re-initialize flatpickr for these inputs to ensure they work properly
                    timeContainer.querySelectorAll('.flatpickr-time').forEach(input => {
                        if (!input._flatpickr) {
                            flatpickr(input, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "h:i K",
                                time_24hr: false
                            });
                        }
                    });
                }
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get the Select All checkbox
            const selectAllCheckbox = document.querySelector('.select-all');
            // Get all individual day checkboxes
            const dayCheckboxes = document.querySelectorAll('.day-checkbox');

            // Handle availability checkboxes (time-picker-calendar2)
            $(document).on('change', '.time-picker-calendar2 input[type="checkbox"]', function () {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();

                    // Initialize flatpickr for time inputs
                    $checkedTime.find('.flatpickr-time').each(function () {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "h:i K",
                                time_24hr: false
                            });
                        }
                    });
                } else {
                    $checkedTime.hide();
                    $closedTime.show();

                    // Clear time values
                    $checkedTime.find('.flatpickr-time').val('');
                }
            });

            // Initialize existing state for availability checkboxes
            $('.time-picker-calendar2 input[type="checkbox"]').each(function () {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();
                } else {
                    $checkedTime.hide();
                    $closedTime.show();
                }
            });

            // Function to toggle time picker visibility and Flatpickr
            function toggleTimePicker(checkbox, isChecked) {
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector('.start-time');
                const timeInputs = timePickerContainer.querySelectorAll('.flatpickr-time');

                if (isChecked) {
                    timePickerContainer.style.display = 'flex';
                    timeInputs.forEach(input => {
                        flatpickr(input, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "h:i K",
                            time_24hr: false
                        });
                    });
                } else {
                    timePickerContainer.style.display = 'none';
                    timeInputs.forEach(input => {
                        if (input._flatpickr) {
                            input._flatpickr.destroy();
                        }
                    });
                }
            }

            // Event listener for Select All checkbox
            selectAllCheckbox.addEventListener('change', function () {
                const isChecked = this.checked;

                // Toggle all day checkboxes and their time pickers
                dayCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked; // Check or uncheck all
                    toggleTimePicker(checkbox, isChecked); // Show or hide time picker
                });
            });

            // Existing logic for individual checkboxes
            dayCheckboxes.forEach(checkbox => {
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector(
                    '.start-time');

                // Only hide time picker if checkbox is not checked or if it doesn't have time values
                if (!checkbox.checked) {
                    timePickerContainer.style.display = 'none';
                } else {
                    // If checkbox is checked, check if there are time values
                    const timeInputs = timePickerContainer.querySelectorAll('.flatpickr-time');
                    const hasTimeValues = Array.from(timeInputs).some(input => input.value.trim() !== '');

                    if (hasTimeValues) {
                        // Keep it visible and ensure flatpickr is initialized
                        timePickerContainer.style.display = 'flex';
                        timeInputs.forEach(input => {
                            if (!input._flatpickr) {
                                flatpickr(input, {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "h:i K",
                                    time_24hr: false
                                });
                            }
                        });
                    } else {
                        timePickerContainer.style.display = 'none';
                    }
                }

                // Add event listener for individual checkbox change
                checkbox.addEventListener('change', function () {
                    toggleTimePicker(this, this.checked);
                });
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            const allowedDocs = [
                "application/pdf", "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-powerpoint",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                "application/zip"
            ];
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];

            // Generic handler for multiple dropzones
            $("[data-type]").each(function () {
                const dropArea = $(this);
                const fileInput = dropArea.find(".file-input");
                const previewContainer = dropArea.find(".preview-container");
                const type = dropArea.data("type");

                dropArea.on("click", function () {
                    fileInput.click();
                });

                fileInput.on("change", function (e) {
                    handleFiles(e.target.files, type, previewContainer);
                });

                dropArea.on("dragover", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#ddd");
                });

                dropArea.on("dragleave", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#f9f9f9");
                });

                dropArea.on("drop", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#f9f9f9");
                    handleFiles(e.originalEvent.dataTransfer.files, type, previewContainer);
                });
            });

            function handleFiles(files, type, previewContainer) {
                $.each(files, function (index, file) {
                    const fileType = file.type;
                    const fileReader = new FileReader();

                    if (type === "certificate") {
                        if (!allowedImages.includes(fileType)) {
                            alert("Only image files (PNG, JPG, JPEG) are allowed!");
                            return;
                        }

                        fileReader.onload = function (e) {
                            const previewElement = $("<div class='preview-box'></div>");
                            previewElement.append(`<img src="${e.target.result}" alt="Image Preview">`);
                            const removeBtn = $("<button class='remove-image'>X</button>");
                            removeBtn.on("click", function () {
                                previewElement.remove();
                            });
                            previewElement.append(removeBtn);
                            previewContainer.append(previewElement);
                        };

                    } else if (type === "portfolio") {
                        if (!allowedDocs.includes(fileType)) {
                            alert("Only document files (PDF, DOCX, ZIP, etc.) are allowed!");
                            return;
                        }

                        fileReader.onload = function () {
                            const fileName = file.name;
                            const fileDate = new Date().toLocaleDateString();
                            const previewElement = $("<div class='preview-box-file'></div>");

                            let icon = "<i class='fa-solid fa-file' style='color: black;'></i>";
                            if (fileType === "application/pdf") icon =
                                "<i class='fa-solid fa-file-pdf' style='color: #d70e0ef2;'></i>";
                            if (fileType.includes("word")) icon =
                                "<i class='fa-solid fa-file-word' style='color: #1d517f;'></i>";
                            if (fileType.includes("excel")) icon =
                                "<i class='fa-solid fa-file-excel' style='color: #137b13;'></i>";
                            if (fileType.includes("powerpoint")) icon =
                                "<i class='fa-solid fa-file-powerpoint' style='color: orange;'></i>";
                            if (fileType === "application/zip") icon =
                                "<i class='fa-solid fa-file-zipper' style='color: brown;'></i>";

                            previewElement.append(icon);
                            previewElement.append(
                                `<div class="file-name d-flex flex-column">
                                                        <p class="dark-charcoal manrope fs-14 fw-500">${fileName}</p>
                                                        <p class="dark-charcoal manrope fs-14 normal">${fileDate}</p>
                                                     </div>`
                            );
                            const removeBtn = $("<button class='remove-file'>X</button>");
                            removeBtn.on("click", function () {
                                previewElement.remove();
                            });
                            previewElement.append(removeBtn);
                            previewContainer.append(previewElement);
                        };
                    }

                    fileReader.readAsDataURL(file);
                });
            }
        });
    </script>

    <script>
        var croppedProfileImage = null;
        var croppedProfileImageName = null;

        const profilePreviewNode = document.querySelector("#profile_dzTemplate");
        const profilePreviewTemplate = profilePreviewNode.parentNode.innerHTML;
        profilePreviewNode.remove();

        cropDrop = new Dropzone("#profileImg", {
            url: 'ajaxTest.php', // This won't be used
            maxFiles: 1,
            acceptedFiles: ".png,.jpg,.jpeg",
            thumbnailWidth: 860,
            thumbnailHeight: 332,
            previewTemplate: profilePreviewTemplate,
            previewsContainer: "#profile_dzPreviews",
            autoProcessQueue: true, // Needed to allow transformFile()
            dictRemoveFileConfirmation: "", // Set to empty string to disable confirmation

            init: function() {
                this.on("addedfile", file => {
                    if (!file.type.match(/image.*/)) {
                        this.removeFile(file);
                        alert("Only image files are allowed.");
                    }
                });

                this.on("removedfile", () => {
                    croppedProfileImage = null;
                    croppedProfileImageName = null;
                    $('#bannerImage').val(''); // Clear the banner image input value
                    $('#profileImg').fadeIn(100);
                });

                // Add custom click handler for remove button
                this.on("addedfile", (file) => {
                    // Wait for the preview to be added to DOM
                    setTimeout(() => {
                        const removeBtn = file.previewElement.querySelector('.custom-remove-btn');
                        if (removeBtn) {
                            removeBtn.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                // Directly remove the file without confirmation
                                this.removeFile(file);
                            });
                        }
                    }, 100);
                });

                // 🛑 Intercept actual upload and prevent it
                this.on("sending", function (file, xhr, formData) {
                    // Abort actual upload
                    setTimeout(() => xhr.abort(), 0);
                });

                this.on("error", function (file, message) {
                    console.log("Upload prevented:", message);
                });
            },

            transformFile: function (file, done) {
                const modal = document.getElementById("cropmodal");
                const template = document.getElementById("modaltpl").content.cloneNode(true);
                const modalBody = template.querySelector(".modal-body");
                const modalFooter = template.querySelector(".modal-footer");
                const imgContainer = template.querySelector(".img-container");

                const image = new Image();
                image.src = URL.createObjectURL(file);
                imgContainer.innerHTML = "";
                imgContainer.appendChild(image);

                let cropper;
                const confirmBtn = document.createElement("button");
                confirmBtn.type = "button";
                confirmBtn.className = "blue-btn w-200px py-3";
                confirmBtn.textContent = "Crop";
                confirmBtn.addEventListener("click", function () {
                    const canvas = cropper.getCroppedCanvas({
                        width: 2520,
                        height: 1080
                    });
                    canvas.toBlob(blob => {
                        // Convert blob to base64 string
                        const reader = new FileReader();
                        reader.onloadend = function () {
                            const base64data = reader.result;
                            $('#bannerImage').val(base64data);
                        };
                        reader.readAsDataURL(blob);
                        cropDrop.createThumbnail(blob, cropDrop.options.thumbnailWidth, cropDrop
                            .options.thumbnailHeight, cropDrop.options.thumbnailMethod,
                            false,
                            function (dataURL) {
                                cropDrop.emit("thumbnail", file, dataURL);
                                done(blob); // Still needed for Dropzone preview

                                croppedProfileImage = blob;
                                croppedProfileImageName = file.name;

                                $('#cropmodal').modal('hide');
                                cropper.destroy();
                                modal.innerHTML = '';
                            });
                    });

                });

                modalFooter.appendChild(confirmBtn);
                modal.appendChild(template);

                $('#cropmodal').modal('show');
                $('#cropmodal').on('shown.bs.modal', function () {
                    cropper = new Cropper(image, {
                        aspectRatio: 21 / 9,
                        viewMode: 1
                    });
                });

                $('#cropmodal').on('hidden.bs.modal', function () {
                    if (cropper) cropper.destroy();
                    modal.innerHTML = '';
                    $('#profileImg').fadeOut(100);
                });
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            let holidayIndex = 0; // Initialize holidayIndex

            // Reindex holidays function moved to global scope
            function reindexHolidays() {
                // Reindex regular holidays (non-custom)
                const regularHolidayContainers = $('.time-picker-calendar').not('.custom-holiday-div');
                regularHolidayContainers.each(function (index) {
                    const inputs = $(this).find('input, select, textarea');
                    inputs.each(function () {
                        const name = $(this).attr('name');
                        if (name && name.includes('holidays[')) {
                            const newName = name.replace(/holidays\[\d+\]/, 'holidays[' + index +
                                ']');
                            $(this).attr('name', newName);
                        }
                    });
                });

                // Reindex custom holidays separately (starting from 0)
                const customHolidayContainers = $('.custom-holiday-div');
                customHolidayContainers.each(function (index) {
                    const inputs = $(this).find('input, select, textarea');
                    inputs.each(function () {
                        const name = $(this).attr('name');
                        if (name && name.includes('custom_holidays[')) {
                            const newName = name.replace(/custom_holidays\[\d+\]/,
                                'custom_holidays[' + index + ']');
                            $(this).attr('name', newName);
                        }
                    });
                });

                // Update holidayIndex to be the count of custom holidays only
                holidayIndex = customHolidayContainers.length;
            }

            // Delegate for checkbox and delete interactions
            $(document).on('change', function (e) {
                if ($(e.target).hasClass('day-checkbox')) {
                    const checkbox = $(e.target);
                    const timePickerContainer = checkbox.closest('.time-picker-calendar').find(
                        '.start-time');

                    if (checkbox.prop('checked')) {
                        timePickerContainer.show();
                        timePickerContainer.find('.flatpickr-time').each(function () {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "h:i K",
                                time_24hr: false
                            });
                        });
                    } else {
                        timePickerContainer.hide();
                        timePickerContainer.find('.flatpickr-time').each(function () {
                            if (this._flatpickr) this._flatpickr.destroy();
                        });
                    }
                }

                if ($(e.target).hasClass('select-all')) {
                    const isChecked = $(e.target).prop('checked');
                    $('.day-checkbox').prop('checked', isChecked).trigger('change');
                }
            });

            // Save custom holiday
            $('#saveCustomHoliday').on('click', function () {
                const name = $('#customHolidayName').val().trim();
                const date = $('#customHolidayDate').val().trim();


                if (name && date) {
                    console.log(holidayIndex);
                    const wrapper = $('<div>', {
                        class: 'time-picker-calendar custom-holiday-div'
                    }).html(`
                                            <div class="d-flex justify-content-between align-items-center">
                                                <label class="days">
                                                    <input type="checkbox" class="day-checkbox" name="custom_holidays[${holidayIndex}]">
                                                    <span class="checkmark">${name}</span>
                                                    <input type="hidden" name="custom_holidays[${holidayIndex}][name]" value="${name}">
                                                    <input type="hidden" name="custom_holidays[${holidayIndex}][date]" value="${date}">
                                                </label>
                                                <p>${date}</p>
                                            </div>
                                            <button class="delete-holiday delete-block2 mb-8">Delete</button>
                                            <div class="start-time" style="display: none;">
                                                <div class="d-flex gap-10">
                                                    <input type="text" class="flatpickr-time no_validate" placeholder="Select Time" name="custom_holidays[${holidayIndex}][start_time]">
                                                    <p> - </p>
                                                    <input type="text" class="flatpickr-time no_validate" placeholder="Select Time" name="custom_holidays[${holidayIndex}][end_time]">
                                                </div>
                                            </div>`);

                    $('.add-custom-holiday-btn').before(wrapper);

                    // Reindex all holidays to ensure proper sequential numbering
                    reindexHolidays();

                    const checkbox = wrapper.find('.day-checkbox');
                    const timePickerContainer = wrapper.find('.start-time');

                    // Bind checkbox behavior
                    checkbox.on('change', function() {
                        if ($(this).prop('checked')) {
                            // Enable all inputs when checked
                            $hiddenInputs.prop('disabled', false);
                            $timeInputs.prop('disabled', false);

                            timePickerContainer.show();
                            timePickerContainer.find('.flatpickr-time').each(function () {
                                flatpickr(this, {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "h:i K",
                                    time_24hr: false
                                });
                            });
                        } else {
                            // Disable all inputs when unchecked
                            $hiddenInputs.prop('disabled', true);
                            $timeInputs.prop('disabled', true);
                            $timeInputs.val(''); // Clear time values

                            timePickerContainer.hide();
                            timePickerContainer.find('.flatpickr-time').each(function () {
                                if (this._flatpickr) this._flatpickr.destroy();
                            });
                        }
                    });

                    // Auto-check and trigger change
                    checkbox.prop('checked', true).trigger('change');

                    // Clear modal and hide
                    $('#customHolidayName').val('');
                    $('#customHolidayDate').val('');
                    $('#customHolidayModal').hide();
                } else {
                    alert('Please fill in both holiday name and date.');
                }
            });

            // Delete handler
            $(document).on('click', '.delete-holiday', function () {
                const wrapper = $(this).closest('.custom-holiday-div');
                if (wrapper.length) {
                    wrapper.remove();
                    reindexHolidays();
                }
            });


            // Modal controls
            document.querySelector('.add-custom-holiday-btn').addEventListener('click', function () {
                document.getElementById('customHolidayModal').style.display = 'flex';
            });

            document.querySelector('.modal .close').addEventListener('click', function () {
                document.getElementById('customHolidayModal').style.display = 'none';
            });

            // Initialize flatpickr date input
            flatpickr('#customHolidayDate', {
                dateFormat: "F j"
            });

            // Auto-check the first custom holiday checkbox
            const firstCustomCheckbox = document.querySelector('.custom-holiday-div .day-checkbox');
            if (firstCustomCheckbox) {
                firstCustomCheckbox.checked = true;
                firstCustomCheckbox.dispatchEvent(new Event('change'));
            }

            // Initialize holidayIndex based on existing custom holidays only
            const existingHolidays = document.querySelectorAll('.custom-holiday-div');
            holidayIndex = existingHolidays.length;
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.service_category').click(function() {
                var categoryId = $(this).data('ctg');
                var ctg_parent = $(this).closest('.parent_services');
                var sub_ctg = ctg_parent.find('.sub_categories');

                // Toggle active state instead of removing from all
                $(this).toggleClass('active');

                // Update hidden inputs for categories
                updateCategoryInputs();

                // Show/hide subcategories based on active state
                if ($(this).hasClass('active')) {
                    sub_ctg.find('.sub_category[data-sub-ctg="' + categoryId + '"]').addClass('show').show();
                } else {
                    sub_ctg.find('.sub_category[data-sub-ctg="' + categoryId + '"]').removeClass('show').hide();
                    // Uncheck all subcategories for this category when category is deselected
                    sub_ctg.find('.sub_category[data-sub-ctg="' + categoryId + '"] input[type="checkbox"]').prop('checked', false);
                }
            });

            // Function to update hidden category inputs
            function updateCategoryInputs() {
                var $container = $('#selected-categories-inputs');
                $container.empty();

                $('.service_category.active').each(function(index) {
                    var categoryId = $(this).data('ctg');
                    $container.append('<input type="hidden" name="categories[]" value="' + categoryId + '">');
                });
            }
        });

        // Handle certificate exception checkbox
        $(document).on('change', 'input[name*="[exception]"]', function() {
            var $checkbox = $(this);
            var $block = $checkbox.closest('.file-upload-group');
            var $textarea = $block.find('.exception-textarea');
            var $uploadBox = $block.find('.upload-box');

            if ($checkbox.is(':checked')) {
                $textarea.show();
                $uploadBox.css('opacity', '0.5'); // Make upload optional visually
            } else {
                $textarea.hide();
                $block.find('textarea[name*="[exception_reason]"]').val(''); // Clear reason
                $uploadBox.css('opacity', '1'); // Make upload required visually
            }
        });

        // Handle custom holiday checkbox changes
        $(document).on('change', 'input[name^="custom_holidays"][type="checkbox"]', function() {
            var $checkbox = $(this);
            var $container = $checkbox.closest('.custom-holiday-div');
            var $hiddenInputs = $container.find('input[type="hidden"]');
            var $timeInputs = $container.find('input[name*="[start_time]"], input[name*="[end_time]"]');

            if ($checkbox.is(':checked')) {
                // Enable hidden inputs when checked
                $hiddenInputs.prop('disabled', false);
                $timeInputs.prop('disabled', false);
            } else {
                // Disable hidden inputs when unchecked to prevent them from being sent
                $hiddenInputs.prop('disabled', true);
                $timeInputs.prop('disabled', true);
                // Clear time values
                $timeInputs.val('');
            }
        });

        // Initialize exception checkbox state on page load
        $(document).ready(function() {
            $('input[name*="[exception]"]').each(function() {
                var $checkbox = $(this);
                var $block = $checkbox.closest('.file-upload-group');
                var $textarea = $block.find('.exception-textarea');
                var $uploadBox = $block.find('.upload-box');

                if ($checkbox.is(':checked')) {
                    $textarea.show();
                    $uploadBox.css('opacity', '0.5');
                } else {
                    $textarea.hide();
                    $uploadBox.css('opacity', '1');
                }
            });

            // Initialize custom holiday checkbox state on page load
            $('input[name^="custom_holidays"][type="checkbox"]').each(function() {
                var $checkbox = $(this);
                var $container = $checkbox.closest('.custom-holiday-div');
                var $hiddenInputs = $container.find('input[type="hidden"]');
                var $timeInputs = $container.find('input[name*="[start_time]"], input[name*="[end_time]"]');

                if ($checkbox.is(':checked')) {
                    $hiddenInputs.prop('disabled', false);
                    $timeInputs.prop('disabled', false);
                } else {
                    $hiddenInputs.prop('disabled', true);
                    $timeInputs.prop('disabled', true);
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.includes.google-map', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/auth/register/professional.blade.php ENDPATH**/ ?>