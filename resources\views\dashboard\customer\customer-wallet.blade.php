@extends('website.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container addfamily padding-block">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="sora black">Wallet</h4>
                </div>
                <div class="col-md-12 family-tabs">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active service-tab" id="payment-method-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-payment-method" type="button" role="tab"
                                aria-controls="pills-payment-method" aria-selected="true">Payment Method
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link service-tab" id="transaction-history-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-transaction-history" type="button" role="tab"
                                aria-controls="pills-transaction-history" aria-selected="true">Transaction History
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link service-tab" id="refund-history-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-refund-history" type="button" role="tab"
                                aria-controls="pills-refund-history" aria-selected="true">Refund History
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link service-tab" id="refund-history-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-dispute-transaction" type="button" role="tab"
                                aria-controls="pills-dispute-transaction" aria-selected="true">Dispute Transaction
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-payment-method" role="tabpanel"
                            aria-labelledby="payment-method-tab" tabindex="0">
                            <div>
                                <p class="fs-16 sora semi_bold black">Payment Method</p>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="card card-box p-5">
                                                    <div
                                                        class="card-body d-flex justify-content-between align-items-center p-0">
                                                        <div>
                                                            <p class="mb-2 fs-15 black regular">Maria Williams <span
                                                                    class="badge white fs-10px regular  blue-badge">Primary</span>
                                                            </p>
                                                            <div class="d-flex gap-4">
                                                                <div class="card-box px-3 py-2">
                                                                    <img src="{{asset('website')}}/assets/images/Mastercard.svg"
                                                                        class="h-25px w-25px rounded-3 object-fit-contain"
                                                                        alt="card-image" />
                                                                </div>
                                                                <div>
                                                                    <p class="m-0 fs-14 normal black">Visa •••• 9016
                                                                    </p>
                                                                    <p class="m-0 fs-12 light-black opacity-6 normal">
                                                                        Debit-Expires 04/20</p>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div>
                                                            <a href="#!" data-bs-target="#editCardModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="drop-btn btn btn-outline-secondary btn-sm me-2 py-2 px-3 text-center"><i
                                                                    class="bi bi-pencil p-0"></i></a>
                                                            <button
                                                                class="drop-btn btn delete-btn  btn-outline-danger btn-sm py-2 px-3 text-center"><i
                                                                    class="bi bi-trash p-0"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="card card-box p-5">
                                                    <div
                                                        class="card-body d-flex justify-content-between align-items-center p-0">
                                                        <div>
                                                            <p class="mb-2 fs-15 black regular">Maria Williams</p>
                                                            <div class="d-flex gap-4">
                                                                <div class="card-box px-3 py-2">
                                                                    <img src="{{asset('website')}}/assets/images/visa-logo.svg"
                                                                        class="h-25px w-25px rounded-3 object-fit-contain"
                                                                        alt="card-image" />
                                                                </div>
                                                                <div>
                                                                    <p class="m-0 fs-14 normal black">Visa •••• 9016
                                                                    </p>
                                                                    <p class="m-0 fs-12 light-black opacity-6 normal">
                                                                        Debit-Expires 04/20</p>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div>
                                                            <a href="#!" data-bs-target="#editCardModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="drop-btn btn btn-outline-secondary btn-sm me-2 py-2 px-3 text-center"><i
                                                                    class="bi bi-pencil p-0"></i></a>
                                                            <button
                                                                class="drop-btn btn delete-btn  btn-outline-danger btn-sm py-2 px-3 text-center"><i
                                                                    class="bi bi-trash p-0"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <a href="#!" data-bs-toggle="modal" data-bs-target="#addCardModalLabel"
                                            class="fs-16 deep-blue semi_bold d-flex card-box justify-content-center align-items-center flex-column">
                                            <img src="{{asset('website')}}/assets/images/add-card.png"
                                                class="h-125px w-125px  object-fit-contain" alt="card-image" />
                                            <span>
                                                <i class="fa-solid fa-plus me-4"></i>Add new card
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="tab-pane fade" id="pills-transaction-history" role="tabpanel"
                            aria-labelledby="transaction-history-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <p class="fs-16 sora black semi_bold">Transaction History</p>
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="customSearchInput"
                                                    placeholder="Search..." />
                                            </div>
                                            <!-- Select with dots -->
                                            <div class="dropdown search_box select-box">
                                                <button
                                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span><span class="dot"></span>
                                                        All</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                            data-color="#4B5563"><span class="dot all"></span>
                                                            All</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Complete" data-color="#10B981"><span
                                                                class="dot completed"></span>
                                                            Paid</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Canceled" data-color="#EF4444"><span
                                                                class="dot cancelled-dot"></span>
                                                            Unpaid</a></li>
                                                </ul>
                                            </div>
                                            <!-- Date Picker -->
                                            <label for="datePicker" class="date_picker">
                                                <div class="date-picker-container">
                                                    <i class="bi bi-calendar-event calender-icon"></i>
                                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                                </div>
                                            </label>
                                            <div class="search_box d-block ms-auto">
                                                <a href="#!" class="search_input fs-14 normal link-gray ">
                                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <table id="responsiveTable"
                                            class="responsiveTable display nowrap wallet-history-table w-100">
                                            <thead>
                                                <tr>
                                                    <th>Customer Name</th>
                                                    <th>Service Type</th>
                                                    <th>Client</th>
                                                    <th>Amount</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for ($b = 0; $b < 20; $b++)
                                                    <tr>
                                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                                        <td data-label="Service Type">Hair Cutting & Styling</td>
                                                        <td data-label="Client">Walk-in</td>
                                                        <td data-label="Amount">$17.84</td>
                                                        <td data-label="Status" class="paid-status status">Paid</td>
                                                        <td data-label="Date">20 Aug, 2024</td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButtonTable" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButtonTable">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                                        <td data-label="Service Type">Hair Cutting & Styling</td>
                                                        <td data-label="Client">Walk-in</td>
                                                        <td data-label="Amount">$17.84</td>
                                                        <td data-label="Status" class="unpaid-status status">Unpaid</td>
                                                        <td data-label="Date">20 Aug, 2024</td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButtonTable" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButtonTable">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endfor

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-refund-history" role="tabpanel"
                            aria-labelledby="refund-history-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <p class="fs-16 sora black semi_bold">Refund History</p>
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="customSearchInput"
                                                    placeholder="Search..." />
                                            </div>
                                            <!-- Select with dots -->
                                            <div class="dropdown search_box select-box">
                                                <button
                                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span><span class="dot"></span>
                                                        All</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                            data-color="#4B5563"><span class="dot all"></span>
                                                            All</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Complete" data-color="#10B981"><span
                                                                class="dot completed"></span>
                                                            Paid</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Canceled" data-color="#EF4444"><span
                                                                class="dot cancelled-dot"></span>
                                                            Unpaid</a></li>
                                                </ul>
                                            </div>
                                            <!-- Date Picker -->
                                            <label for="datePicker" class="date_picker">
                                                <div class="date-picker-container">
                                                    <i class="bi bi-calendar-event calender-icon"></i>
                                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                                </div>
                                            </label>
                                            <div class="search_box d-block ms-auto">
                                                <a href="#!" class="search_input fs-14 normal link-gray ">
                                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <table id="responsiveTable"
                                            class="responsiveTable display wallet-history-table w-100">
                                            <thead>
                                                <tr>
                                                    <th>Customer Name</th>
                                                    <th>Service Type</th>
                                                    <th>Client</th>
                                                    <th>Amount</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for ($b = 0; $b < 20; $b++)
                                                    <tr>
                                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                                        <td data-label="Service Type">Hair Cutting & Styling</td>
                                                        <td data-label="Client">Walk-in</td>
                                                        <td data-label="Amount">$17.84</td>
                                                        <td data-label="Status" class="paid-status status">Paid</td>
                                                        <td data-label="Date">20 Aug, 2024</td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button" id="dropdownMenuButton2"
                                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                                        <td data-label="Service Type">Hair Cutting & Styling</td>
                                                        <td data-label="Client">Walk-in</td>
                                                        <td data-label="Amount">$17.84</td>
                                                        <td data-label="Status" class="unpaid-status status">Unpaid</td>
                                                        <td data-label="Date">20 Aug, 2024</td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button" id="dropdownMenuButton2"
                                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endfor

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-dispute-transaction" role="tabpanel"
                            aria-labelledby="dispute-transaction-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <p class="fs-16 sora black semi_bold">Refund History</p>
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="customSearchInput"
                                                    placeholder="Search..." />
                                            </div>
                                            <!-- Date Picker -->
                                            <label for="datePicker" class="date_picker">
                                                <div class="date-picker-container">
                                                    <i class="bi bi-calendar-event calender-icon"></i>
                                                    <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                                </div>
                                            </label>
                                            <div class="search_box d-block ms-auto">
                                                <a href="#!" class="search_input fs-14 normal link-gray ">
                                                    Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
                                                </a>
                                            </div>

                                        </div>
                                        <table id="responsiveTable"
                                            class="responsiveTable display wallet-history-table w-100">
                                            <thead>
                                                <tr>
                                                    <th>Customer Name</th>
                                                    <th>Service Type</th>
                                                    <th>Client</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for ($b = 0; $b < 20; $b++)
                                                    <tr>
                                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                                        <td data-label="Service Type">Hair Cutting & Styling</td>
                                                        <td data-label="Client">Walk-in</td>
                                                        <td data-label="Amount">$17.84</td>
                                                        <td data-label="Date">20 Aug, 2024</td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button" id="dropdownMenuButton2"
                                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Resolve
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Rejected
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td data-label="Customer Name">Carolyn Perkins</td>
                                                        <td data-label="Service Type">Hair Cutting & Styling</td>
                                                        <td data-label="Client">Walk-in</td>
                                                        <td data-label="Amount">$17.84</td>
                                                        <td data-label="Date">20 Aug, 2024</td>
                                                        <td data-label="Action">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button" id="dropdownMenuButton2"
                                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Resolve
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Rejected
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endfor

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('dashboard.templates.modal.edit-card-modal')
    @include('dashboard.templates.modal.add-card-modal')
@endsection
@push('js')
@endpush
