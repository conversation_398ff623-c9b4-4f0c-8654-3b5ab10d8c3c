<div class="modal fade card-details" id="intro-cards-modal" aria-labelledby="intro-cards-modal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Intro Cards</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="intro-cards-form" action="<?php echo e(route('intro-cards.save')); ?>" method="POST"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <div id="intro-cards-wrapper">
                                <?php if(auth()->user()->introCards && auth()->user()->introCards->count() > 0): ?>
                                    <?php $__currentLoopData = auth()->user()->introCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $introCard): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="gray-card my-5 intro-card-block" data-index="<?php echo e($index); ?>">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h4 class="mb-0">Card #<?php echo e($index + 1); ?></h4>
                                                <?php if($index > 0): ?>
                                                    <button type="button" class="btn btn-sm btn-danger delete-intro-card">
                                                        <i class="bi bi-trash"></i> Delete
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                            <div class="row row-gap-4">
                                                <div class="col-md-12">
                                                    <label for="intro-image-<?php echo e($index); ?>" class="form-label form-input-labels">Image</label>
                                                    <div class="position-relative form-add-services">
                                                        <div class="image-input image-input-empty image-input-circle"
                                                            data-kt-image-input="true"
                                                            style="background-image: url(<?php echo e(asset('website') . '/' . ($introCard->image ?? 'website/assets/images/image_input_holder.png')); ?>)">
                                                            <div class="image-input-wrapper w-125px h-125px"></div>
                                                            <label class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                                data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click" title="Change image">
                                                                <i class="ki-duotone ki-pencil fs-6">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                                <input type="file" name="introCards[<?php echo e($index); ?>][image]" accept=".png, .jpg, .jpeg" />
                                                                <input type="hidden" name="introCards[<?php echo e($index); ?>][old_image]" value="<?php echo e($introCard->image ?? ''); ?>" />
                                                                <input type="hidden" name="avatar_remove" />
                                                            </label>
                                                            <span class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click" title="Cancel">
                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                            </span>
                                                            <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click" title="Remove">
                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <label for="intro-heading-<?php echo e($index); ?>" class="form-label form-input-labels">Heading</label>
                                                    <input type="text" class="form-control form-inputs-field"
                                                        placeholder="Enter Heading" id="intro-heading-<?php echo e($index); ?>"
                                                        name="introCards[<?php echo e($index); ?>][heading]" value="<?php echo e($introCard->heading ?? ''); ?>">
                                                </div>
                                                <div class="col-md-12">
                                                    <label for="intro-description-<?php echo e($index); ?>" class="form-label form-input-labels">Description</label>
                                                    <textarea class="form-control form-inputs-field" rows="3"
                                                        placeholder="Enter description" id="intro-description-<?php echo e($index); ?>"
                                                        name="introCards[<?php echo e($index); ?>][description]"><?php echo e($introCard->description ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <div class="gray-card my-5 intro-card-block" data-index="0">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h4 class="mb-0">Card #1</h4>
                                        </div>
                                        <div class="row row-gap-4">
                                            <div class="col-md-12">
                                                <label for="intro-image-0" class="form-label form-input-labels">Image</label>
                                                <div class="position-relative form-add-services">
                                                    <div class="image-input image-input-empty image-input-circle"
                                                        data-kt-image-input="true"
                                                        style="background-image: url('<?php echo e(asset('website/assets/images/image_input_holder.png')); ?>')">
                                                        <div class="image-input-wrapper w-125px h-125px"></div>
                                                        <label class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                            data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Change image">
                                                            <i class="ki-duotone ki-pencil fs-6">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                            </i>
                                                            <input type="file" name="introCards[0][image]" accept=".png, .jpg, .jpeg" />
                                                            <input type="hidden" name="avatar_remove" />
                                                        </label>
                                                        <span class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Cancel">
                                                            <i class="ki-outline ki-cross fs-3"></i>
                                                        </span>
                                                        <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                            data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                            data-bs-dismiss="click" title="Remove">
                                                            <i class="ki-outline ki-cross fs-3"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <label for="intro-heading-0" class="form-label form-input-labels">Heading</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter Heading" id="intro-heading-0" name="introCards[0][heading]">
                                            </div>
                                            <div class="col-md-12">
                                                <label for="intro-description-0" class="form-label form-input-labels">Description</label>
                                                <textarea class="form-control form-inputs-field" rows="3"
                                                    placeholder="Enter description" id="intro-description-0"
                                                    name="introCards[0][description]"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Add More Button -->
                            <div class="col-md-12 text-center">
                                <button type="button" class="addMoreBtn" id="add-more-intro-card">
                                    <i class="bi bi-plus-circle me-2"></i>Add More Intro Card
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>


<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/profile_settings/modal/intro-cards-modal.blade.php ENDPATH**/ ?>