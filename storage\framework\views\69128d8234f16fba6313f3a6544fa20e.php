<div class="d-flex gap-3 align-items-center flex-wrap">
    <!-- Search Input -->
    <div class="search_box">
        <label for="customSearchInput">
            <i class="fas fa-search"></i>
        </label>
        <input class="search_input search" type="text" id="customSearchInput" placeholder="Search..." />
    </div>

    <!-- Status Dropdown -->
    <div class="dropdown search_box select-box">
        <button class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                type="button" data-bs-toggle="dropdown" aria-expanded="false">
            <span><span class="dot"></span> All</span>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item dropdown-status" href="#" data-label="All" data-color="#4B5563">
                <span class="dot all"></span> All</a></li>
            <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing" data-color="#F59E0B">
                <span class="dot ongoing"></span> Ongoing</a></li>
            <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming" data-color="#3B82F6">
                <span class="dot upcoming"></span> Upcoming</a></li>
            <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete" data-color="#10B981">
                <span class="dot completed"></span> Complete</a></li>
            <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled" data-color="#EF4444">
                <span class="dot cancelled-dot"></span> Canceled</a></li>
        </ul>
    </div>

    <!-- Category Filter -->
    <div class="search_box select-box">
        <select class="search_input" id="categoryFilter">
            <option value="Category">Category</option>
            <option value="all">All</option>
            <?php if(isset($categories)): ?>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($category->name); ?>"><?php echo e($category->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <option value="Group">Group</option>
                <option value="Individual">Individual</option>
            <?php endif; ?>
        </select>
    </div>

    <!-- Staff Filter (for business users only) -->
    <?php if(auth()->check() && auth()->user()->hasRole('business')): ?>
        <div class="search_box select-box">
            <select class="search_input" id="staffFilter">
                <option value="Staff">Staff</option>
                <option value="all">All</option>
                <?php if(isset($staff)): ?>
                    <?php $__currentLoopData = $staff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staffMember): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($staffMember->name); ?>"><?php echo e($staffMember->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <option value="Group">Group</option>
                    <option value="Individual">Individual</option>
                <?php endif; ?>
            </select>
        </div>
    <?php endif; ?>

    <!-- Date Picker -->
    <label for="datePicker" class="date_picker">
        <div class="date-picker-container">
            <i class="bi bi-calendar-event calender-icon"></i>
            <input type="text" name="datePicker" class="datePicker w-200px ms-3">
            <i class="fa fa-chevron-down down-arrow ms-9"></i>
        </div>
    </label>

    <!-- Export Button -->
    <?php if(isset($showExport) && $showExport): ?>
        <div class="search_box d-block ms-auto">
            <a href="#" id="exportBookingsBtn" class="search_input fs-14 normal link-gray text-decoration-none">
                Export <i class="bi bi-file-arrow-down ms-1 file-icon"></i>
            </a>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/partials/booking-search-filters.blade.php ENDPATH**/ ?>